# Widget Development Manual

## Table of Contents
- [Widget System Overview](#widget-system-overview)
- [Widget Types](#widget-types)
- [Widget Configuration](#widget-configuration)
- [Parameter System](#parameter-system)
- [Data Formatting](#data-formatting)
- [Advanced Features](#advanced-features)
- [Real-time Widgets](#real-time-widgets)
- [Best Practices](#best-practices)
- [Examples](#examples)

## Widget System Overview

OpenBB Workspace widgets are modular UI components that display data from your backend APIs. Each widget is defined by:

1. **Configuration**: JSON specification in `widgets.json`
2. **Data Endpoint**: API endpoint that returns the widget data
3. **Parameters**: User inputs that customize the widget
4. **Rendering**: How data is displayed (table, chart, etc.)

### Basic Widget Structure
```json
{
  "widget_id": {
    "name": "Widget Display Name",
    "description": "Widget description for users and AI",
    "type": "table|chart|markdown|metric|live_grid|newsfeed",
    "endpoint": "api_endpoint_name",
    "category": "Organization category",
    "params": [...],
    "data": {...},
    "gridData": {"w": 12, "h": 8}
  }
}
```

## Widget Types

### 1. Table Widget
Displays data in sortable, filterable tables with optional chart views.

```json
{
  "stock_table": {
    "name": "Stock Data Table",
    "description": "Display stock data in table format",
    "type": "table",
    "endpoint": "stock_data",
    "data": {
      "table": {
        "enableCharts": true,
        "showAll": true,
        "chartView": {
          "enabled": true,
          "chartType": "line",
          "cellRangeCols": {
            "line": ["date", "close", "volume"]
          }
        },
        "columnsDefs": [
          {
            "field": "date",
            "headerName": "Date",
            "chartDataType": "category",
            "cellDataType": "date"
          },
          {
            "field": "close",
            "headerName": "Close Price",
            "chartDataType": "series",
            "cellDataType": "number",
            "formatterFn": "none"
          }
        ]
      }
    }
  }
}
```

**Backend Implementation:**
```python
@register_widget({
    "name": "Stock Data Table",
    "type": "table",
    "endpoint": "stock_data"
})
@app.get("/stock_data")
def stock_data(symbol: str = "AAPL"):
    data = obb.equity.price.historical(symbol)
    df = data.to_dataframe()
    return df.to_dict('records')
```

### 2. Chart Widget
Displays Plotly or Highcharts visualizations.

```json
{
  "price_chart": {
    "name": "Price Chart",
    "description": "Stock price visualization",
    "type": "chart",
    "endpoint": "price_chart",
    "params": [
      {
        "type": "text",
        "paramName": "symbol",
        "value": "AAPL",
        "label": "Symbol"
      }
    ]
  }
}
```

**Backend Implementation:**
```python
import plotly.graph_objects as go

@register_widget({
    "name": "Price Chart",
    "type": "chart",
    "endpoint": "price_chart"
})
@app.get("/price_chart")
def price_chart(symbol: str = "AAPL"):
    data = obb.equity.price.historical(symbol)
    df = data.to_dataframe()
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=df['date'],
        y=df['close'],
        mode='lines',
        name='Close Price'
    ))
    
    fig.update_layout(
        title=f"{symbol} Stock Price",
        xaxis_title="Date",
        yaxis_title="Price ($)"
    )
    
    return fig.to_json()
```

### 3. Metric Widget
Displays single KPI values with optional comparisons.

```json
{
  "stock_metric": {
    "name": "Stock Price Metric",
    "description": "Current stock price display",
    "type": "metric",
    "endpoint": "stock_metric"
  }
}
```

**Backend Implementation:**
```python
@register_widget({
    "name": "Stock Price Metric",
    "type": "metric",
    "endpoint": "stock_metric"
})
@app.get("/stock_metric")
def stock_metric(symbol: str = "AAPL"):
    quote = obb.equity.price.quote(symbol)
    current_price = quote.results[0].last_price
    
    return {
        "value": current_price,
        "title": f"{symbol} Current Price",
        "currency": "USD",
        "change": "+2.5%",
        "changeColor": "green"
    }
```

### 4. Markdown Widget
Displays formatted text, documentation, or reports.

```json
{
  "market_report": {
    "name": "Market Report",
    "description": "Daily market analysis report",
    "type": "markdown",
    "endpoint": "market_report"
  }
}
```

**Backend Implementation:**
```python
@register_widget({
    "name": "Market Report",
    "type": "markdown",
    "endpoint": "market_report"
})
@app.get("/market_report")
def market_report():
    return """
# Daily Market Report

## Market Summary
- **S&P 500**: ****% (4,150.25)
- **NASDAQ**: +0.8% (12,850.75)
- **DOW**: +1.5% (33,250.50)

## Key Movers
- **AAPL**: +2.3% - Strong earnings report
- **TSLA**: -1.8% - Production concerns
- **MSFT**: +1.1% - Cloud growth

## Economic Indicators
- Unemployment: 3.7%
- Inflation (CPI): 3.2%
- GDP Growth: 2.1%
"""
```

### 5. Live Grid Widget
Real-time updating table with WebSocket support.

```json
{
  "live_prices": {
    "name": "Live Stock Prices",
    "description": "Real-time stock price updates",
    "type": "live_grid",
    "endpoint": "live_prices",
    "wsEndpoint": "ws_prices",
    "data": {
      "wsRowIdColumn": "symbol",
      "table": {
        "columnsDefs": [
          {
            "field": "symbol",
            "headerName": "Symbol",
            "enableCellChangeWs": false
          },
          {
            "field": "price",
            "headerName": "Price",
            "enableCellChangeWs": true,
            "renderFn": "showCellChange"
          }
        ]
      }
    }
  }
}
```

**Backend Implementation:**
```python
from fastapi import WebSocket
import asyncio
import json

@register_widget({
    "name": "Live Stock Prices",
    "type": "live_grid",
    "endpoint": "live_prices",
    "wsEndpoint": "ws_prices"
})
@app.get("/live_prices")
def live_prices():
    return [
        {"symbol": "AAPL", "price": 150.25, "change": "****%"},
        {"symbol": "GOOGL", "price": 2800.50, "change": "-0.5%"}
    ]

@app.websocket("/ws_prices")
async def websocket_prices(websocket: WebSocket):
    await websocket.accept()
    
    while True:
        # Simulate price updates
        update = {
            "symbol": "AAPL",
            "price": 150.25 + random.uniform(-1, 1),
            "change": f"{random.uniform(-2, 2):.1f}%"
        }
        
        await websocket.send_text(json.dumps(update))
        await asyncio.sleep(1)
```

## Widget Configuration

### Grid Layout
Control widget size and positioning:

```json
{
  "gridData": {
    "w": 12,        // Width (1-40)
    "h": 8,         // Height (1-100)
    "minW": 6,      // Minimum width
    "minH": 4,      // Minimum height
    "maxW": 24,     // Maximum width
    "maxH": 20      // Maximum height
  }
}
```

### Categories and Organization
```json
{
  "category": "Equity",           // Main category
  "subCategory": "Analysis",      // Subcategory
  "source": ["Custom API"],      // Data source labels
}
```

### Refresh Settings
```json
{
  "refetchInterval": 300000,     // Auto-refresh interval (ms)
  "staleTime": 60000,           // Data staleness threshold (ms)
  "runButton": false            // Show run button instead of auto-refresh
}
```

## Parameter System

### Parameter Types

#### Text Input
```json
{
  "type": "text",
  "paramName": "symbol",
  "value": "AAPL",
  "label": "Stock Symbol",
  "description": "Enter stock ticker symbol",
  "show": true
}
```

#### Date Picker
```json
{
  "type": "date",
  "paramName": "start_date",
  "value": "$currentDate-1y",    // Dynamic date
  "label": "Start Date",
  "description": "Analysis start date"
}
```

#### Dropdown Selection
```json
{
  "type": "text",
  "paramName": "period",
  "value": "1y",
  "label": "Time Period",
  "options": [
    {"label": "1 Month", "value": "1m"},
    {"label": "3 Months", "value": "3m"},
    {"label": "1 Year", "value": "1y"},
    {"label": "5 Years", "value": "5y"}
  ]
}
```

#### Multi-Select Dropdown
```json
{
  "type": "text",
  "paramName": "symbols",
  "value": "AAPL",
  "label": "Stock Symbols",
  "multiSelect": true,
  "multiple": true,
  "options": [
    {"label": "Apple Inc.", "value": "AAPL"},
    {"label": "Microsoft Corp.", "value": "MSFT"},
    {"label": "Google", "value": "GOOGL"}
  ]
}
```

#### Boolean Toggle
```json
{
  "type": "boolean",
  "paramName": "include_volume",
  "value": true,
  "label": "Include Volume Data",
  "description": "Show volume in analysis"
}
```

#### Number Input
```json
{
  "type": "number",
  "paramName": "days",
  "value": 30,
  "label": "Number of Days",
  "description": "Analysis period in days"
}
```

### Dynamic Parameters
```json
{
  "type": "text",
  "paramName": "exchange",
  "value": "NYSE",
  "label": "Exchange",
  "optionsEndpoint": "exchanges",    // Fetch options from endpoint
  "optionsParams": {"country": "$country"}  // Pass other params
}
```

**Backend for Dynamic Options:**
```python
@app.get("/exchanges")
def get_exchanges(country: str = "US"):
    exchanges = {
        "US": [
            {"label": "New York Stock Exchange", "value": "NYSE"},
            {"label": "NASDAQ", "value": "NASDAQ"}
        ],
        "UK": [
            {"label": "London Stock Exchange", "value": "LSE"}
        ]
    }
    return exchanges.get(country, [])
```

## Data Formatting

### Column Definitions
```json
{
  "columnsDefs": [
    {
      "field": "symbol",
      "headerName": "Symbol",
      "chartDataType": "category",
      "cellDataType": "text",
      "width": 100,
      "pinned": "left",
      "hide": false
    },
    {
      "field": "price",
      "headerName": "Price ($)",
      "chartDataType": "series",
      "cellDataType": "number",
      "formatterFn": "none",
      "renderFn": "greenRed",
      "prefix": "$",
      "suffix": " USD"
    }
  ]
}
```

### Formatter Functions
- `int`: Format as integer
- `percent`: Add % symbol
- `normalized`: Multiply by 100
- `normalizedPercent`: Multiply by 100 and add %
- `dateToYear`: Extract year from date
- `none`: No formatting

### Render Functions
- `greenRed`: Color positive/negative values
- `titleCase`: Convert to title case
- `hoverCard`: Show detailed info on hover
- `cellOnClick`: Make cells clickable
- `columnColor`: Conditional column coloring
- `showCellChange`: Highlight changed values

### Advanced Rendering
```json
{
  "field": "company",
  "headerName": "Company",
  "renderFn": "cellOnClick",
  "renderFnParams": {
    "actionType": "sendToAgent",
    "sendToAgent": {
      "markdown": "Analyze **{company}** with market cap of ${market_cap}M"
    }
  }
}
```

## Advanced Features

### Hover Cards
```json
{
  "field": "symbol",
  "renderFn": "hoverCard",
  "renderFnParams": {
    "hoverCard": {
      "cellField": "symbol",
      "title": "Stock Details",
      "markdown": "### {symbol}\n- **Price**: ${price}\n- **Volume**: {volume:,}"
    }
  }
}
```

### Conditional Coloring
```json
{
  "field": "change",
  "renderFn": "columnColor",
  "renderFnParams": {
    "colorRules": [
      {"condition": "gt", "value": 0, "color": "green", "fill": true},
      {"condition": "lt", "value": 0, "color": "red", "fill": true},
      {"condition": "eq", "value": 0, "color": "gray", "fill": false}
    ]
  }
}
```

### Cell Grouping
```json
{
  "field": "sector",
  "renderFn": "cellOnClick",
  "renderFnParams": {
    "actionType": "groupBy",
    "groupByParamName": "sector"
  }
}
```

## Real-time Widgets

### WebSocket Implementation
```python
from fastapi import WebSocket, WebSocketDisconnect
import asyncio
import json

class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            await connection.send_text(message)

manager = ConnectionManager()

@app.websocket("/ws_live_data")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            # Generate or fetch real-time data
            data = get_live_market_data()
            await manager.broadcast(json.dumps(data))
            await asyncio.sleep(1)
    except WebSocketDisconnect:
        manager.disconnect(websocket)
```

### Live Data Updates
```python
def get_live_market_data():
    """Simulate live market data"""
    import random
    
    symbols = ["AAPL", "GOOGL", "MSFT", "TSLA"]
    updates = []
    
    for symbol in symbols:
        price_change = random.uniform(-2, 2)
        updates.append({
            "symbol": symbol,
            "price": round(random.uniform(100, 300), 2),
            "change": f"{price_change:+.2f}%",
            "volume": random.randint(1000000, 10000000),
            "timestamp": datetime.now().isoformat()
        })
    
    return updates
```

## Best Practices

### 1. Widget Design
- Use descriptive names and descriptions
- Choose appropriate widget types for data
- Implement proper error handling
- Optimize for performance

### 2. Data Structure
- Return consistent JSON formats
- Handle missing or null values
- Use appropriate data types
- Implement data validation

### 3. Parameter Design
- Provide sensible defaults
- Use clear labels and descriptions
- Implement parameter validation
- Consider user experience

### 4. Performance
- Implement caching for expensive operations
- Use pagination for large datasets
- Optimize database queries
- Monitor response times

### 5. Error Handling
```python
@app.get("/widget_endpoint")
def widget_endpoint(param: str):
    try:
        # Widget logic
        data = fetch_data(param)
        return data
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid parameter: {e}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")
```

## Examples

### Complete Stock Analysis Widget
```python
@register_widget({
    "name": "Stock Analysis Dashboard",
    "description": "Comprehensive stock analysis with multiple metrics",
    "type": "table",
    "endpoint": "stock_analysis",
    "category": "Equity",
    "gridData": {"w": 24, "h": 16},
    "data": {
        "table": {
            "enableCharts": true,
            "chartView": {
                "enabled": true,
                "chartType": "line"
            },
            "columnsDefs": [
                {
                    "field": "date",
                    "headerName": "Date",
                    "chartDataType": "category"
                },
                {
                    "field": "close",
                    "headerName": "Close Price",
                    "chartDataType": "series",
                    "formatterFn": "none",
                    "prefix": "$"
                },
                {
                    "field": "volume",
                    "headerName": "Volume",
                    "chartDataType": "series",
                    "formatterFn": "int"
                }
            ]
        }
    },
    "params": [
        {
            "type": "text",
            "paramName": "symbol",
            "value": "AAPL",
            "label": "Stock Symbol"
        },
        {
            "type": "date",
            "paramName": "start_date",
            "value": "$currentDate-1y",
            "label": "Start Date"
        }
    ]
})
@app.get("/stock_analysis")
def stock_analysis(symbol: str = "AAPL", start_date: str = None):
    # Fetch stock data
    data = obb.equity.price.historical(symbol, start_date=start_date)
    df = data.to_dataframe()
    
    # Add technical indicators
    df['ma_20'] = df['close'].rolling(20).mean()
    df['ma_50'] = df['close'].rolling(50).mean()
    df['rsi'] = calculate_rsi(df['close'])
    
    return df.to_dict('records')

def calculate_rsi(prices, window=14):
    """Calculate RSI indicator"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))
```

---

*Next: [AI Agents & Copilot Guide](./04-ai-agents-copilot-guide.md)*
