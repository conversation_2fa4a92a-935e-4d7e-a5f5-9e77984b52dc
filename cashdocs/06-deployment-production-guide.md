# Deployment & Production Guide

## Table of Contents
- [Production Architecture](#production-architecture)
- [Containerization](#containerization)
- [Cloud Deployment](#cloud-deployment)
- [Security Implementation](#security-implementation)
- [Performance Optimization](#performance-optimization)
- [Monitoring & Logging](#monitoring--logging)
- [CI/CD Pipeline](#cicd-pipeline)
- [Scaling Strategies](#scaling-strategies)
- [Troubleshooting](#troubleshooting)

## Production Architecture

### Recommended Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    Production Setup                         │
├─────────────────────────────────────────────────────────────┤
│  Load Balancer (nginx/AWS ALB)                             │
├─────────────────────────────────────────────────────────────┤
│  API Gateway (Optional)                                     │
├─────────────────────────────────────────────────────────────┤
│  OpenBB Backend Instances (Multiple)                       │
│  ├── FastAPI Application                                   │
│  ├── Widget Endpoints                                      │
│  └── Health Checks                                         │
├─────────────────────────────────────────────────────────────┤
│  Caching Layer (Redis)                                     │
├─────────────────────────────────────────────────────────────┤
│  Database Layer                                            │
│  ├── Primary Database (PostgreSQL/MySQL)                  │
│  ├── Time Series DB (InfluxDB/TimescaleDB)                │
│  └── Document Store (MongoDB)                              │
├─────────────────────────────────────────────────────────────┤
│  External Services                                          │
│  ├── Data Providers (APIs)                                │
│  ├── Message Queue (RabbitMQ/Kafka)                       │
│  └── File Storage (S3/GCS)                                │
└─────────────────────────────────────────────────────────────┘
```

### Environment Configuration
```python
# config.py
import os
from pydantic import BaseSettings

class Settings(BaseSettings):
    # Application
    app_name: str = "OpenBB Custom Backend"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # Server
    host: str = "0.0.0.0"
    port: int = 8000
    workers: int = 4
    
    # Database
    database_url: str = os.getenv("DATABASE_URL")
    redis_url: str = os.getenv("REDIS_URL", "redis://localhost:6379")
    
    # Security
    secret_key: str = os.getenv("SECRET_KEY")
    api_key: str = os.getenv("API_KEY")
    cors_origins: list = ["https://pro.openbb.co"]
    
    # External APIs
    openbb_api_key: str = os.getenv("OPENBB_API_KEY")
    alpha_vantage_key: str = os.getenv("ALPHA_VANTAGE_KEY")
    
    # Monitoring
    log_level: str = "INFO"
    sentry_dsn: str = os.getenv("SENTRY_DSN")
    
    class Config:
        env_file = ".env"

settings = Settings()
```

## Containerization

### Dockerfile
```dockerfile
# Dockerfile
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        curl \
        libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser
RUN chown -R appuser:appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Run application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose for Development
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************/openbb_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    volumes:
      - .:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: openbb_db
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app

volumes:
  postgres_data:
  redis_data:
```

### Production Docker Compose
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    build: .
    restart: unless-stopped
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - SECRET_KEY=${SECRET_KEY}
    depends_on:
      - db
      - redis
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  db:
    image: postgres:15
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.prod.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app

volumes:
  postgres_data:
  redis_data:
```

## Cloud Deployment

### AWS Deployment with ECS
```yaml
# aws-ecs-task-definition.json
{
  "family": "openbb-backend",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "openbb-backend",
      "image": "your-account.dkr.ecr.region.amazonaws.com/openbb-backend:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "DATABASE_URL",
          "value": "****************************************/db"
        }
      ],
      "secrets": [
        {
          "name": "SECRET_KEY",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:openbb-secrets"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/openbb-backend",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3
      }
    }
  ]
}
```

### Kubernetes Deployment
```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: openbb-backend
  labels:
    app: openbb-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: openbb-backend
  template:
    metadata:
      labels:
        app: openbb-backend
    spec:
      containers:
      - name: openbb-backend
        image: openbb-backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: openbb-secrets
              key: database-url
        - name: REDIS_URL
          value: "redis://redis-service:6379"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: openbb-backend-service
spec:
  selector:
    app: openbb-backend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
```

### Terraform Infrastructure
```hcl
# main.tf
provider "aws" {
  region = var.aws_region
}

# VPC and Networking
resource "aws_vpc" "main" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name = "openbb-vpc"
  }
}

resource "aws_subnet" "private" {
  count             = 2
  vpc_id            = aws_vpc.main.id
  cidr_block        = "10.0.${count.index + 1}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]

  tags = {
    Name = "openbb-private-subnet-${count.index + 1}"
  }
}

# RDS Database
resource "aws_db_instance" "postgres" {
  identifier     = "openbb-postgres"
  engine         = "postgres"
  engine_version = "15.4"
  instance_class = "db.t3.micro"
  
  allocated_storage     = 20
  max_allocated_storage = 100
  storage_encrypted     = true
  
  db_name  = "openbb_db"
  username = var.db_username
  password = var.db_password
  
  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.main.name
  
  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  skip_final_snapshot = true
  
  tags = {
    Name = "openbb-postgres"
  }
}

# ElastiCache Redis
resource "aws_elasticache_subnet_group" "main" {
  name       = "openbb-cache-subnet"
  subnet_ids = aws_subnet.private[*].id
}

resource "aws_elasticache_cluster" "redis" {
  cluster_id           = "openbb-redis"
  engine               = "redis"
  node_type            = "cache.t3.micro"
  num_cache_nodes      = 1
  parameter_group_name = "default.redis7"
  port                 = 6379
  subnet_group_name    = aws_elasticache_subnet_group.main.name
  security_group_ids   = [aws_security_group.redis.id]
  
  tags = {
    Name = "openbb-redis"
  }
}

# ECS Cluster
resource "aws_ecs_cluster" "main" {
  name = "openbb-cluster"
  
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
  
  tags = {
    Name = "openbb-cluster"
  }
}
```

## Security Implementation

### Authentication Middleware
```python
# auth.py
from fastapi import HTTPException, Depends, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import jwt
from datetime import datetime, timedelta

security = HTTPBearer()

class AuthManager:
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
        self.algorithm = "HS256"
    
    def create_access_token(self, data: dict, expires_delta: timedelta = None):
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(hours=24)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, credentials: HTTPAuthorizationCredentials = Depends(security)):
        try:
            payload = jwt.decode(
                credentials.credentials, 
                self.secret_key, 
                algorithms=[self.algorithm]
            )
            username: str = payload.get("sub")
            if username is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Could not validate credentials"
                )
            return username
        except jwt.PyJWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials"
            )

auth_manager = AuthManager(settings.secret_key)

# Protected endpoint example
@app.get("/protected_data")
async def protected_data(current_user: str = Depends(auth_manager.verify_token)):
    return {"message": f"Hello {current_user}", "data": "sensitive_data"}
```

### Rate Limiting
```python
# rate_limiting.py
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import redis

# Redis-based rate limiter
redis_client = redis.Redis.from_url(settings.redis_url)

limiter = Limiter(
    key_func=get_remote_address,
    storage_uri=settings.redis_url
)

app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# Apply rate limiting to endpoints
@app.get("/api/data")
@limiter.limit("100/minute")
async def get_data(request: Request):
    return {"data": "some_data"}

# Custom rate limiting for different user tiers
def get_user_tier(request: Request) -> str:
    # Extract user tier from token or headers
    auth_header = request.headers.get("authorization")
    # Logic to determine user tier
    return "premium"  # or "basic"

@app.get("/api/premium_data")
@limiter.limit("1000/minute", key_func=get_user_tier)
async def get_premium_data(request: Request):
    return {"data": "premium_data"}
```

### Input Validation
```python
# validation.py
from pydantic import BaseModel, validator
from typing import Optional, List
from datetime import datetime

class StockDataRequest(BaseModel):
    symbol: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    period: Optional[str] = "1y"
    
    @validator('symbol')
    def symbol_must_be_valid(cls, v):
        if not v or len(v) > 10:
            raise ValueError('Symbol must be 1-10 characters')
        return v.upper()
    
    @validator('start_date', 'end_date')
    def date_format_valid(cls, v):
        if v:
            try:
                datetime.strptime(v, '%Y-%m-%d')
            except ValueError:
                raise ValueError('Date must be in YYYY-MM-DD format')
        return v
    
    @validator('period')
    def period_must_be_valid(cls, v):
        valid_periods = ['1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y']
        if v not in valid_periods:
            raise ValueError(f'Period must be one of {valid_periods}')
        return v

@app.post("/api/stock_data")
async def get_stock_data(request: StockDataRequest):
    # Request is automatically validated
    return {"symbol": request.symbol, "period": request.period}
```

## Performance Optimization

### Caching Strategy
```python
# caching.py
import redis
import json
import hashlib
from functools import wraps
from typing import Any, Callable

redis_client = redis.Redis.from_url(settings.redis_url)

def cache_result(expiry: int = 3600):
    """Decorator to cache function results"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Create cache key
            cache_key = f"{func.__name__}:{hashlib.md5(str(args + tuple(kwargs.items())).encode()).hexdigest()}"
            
            # Try to get from cache
            cached_result = redis_client.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
            
            # Execute function
            result = await func(*args, **kwargs)
            
            # Cache result
            redis_client.setex(cache_key, expiry, json.dumps(result, default=str))
            
            return result
        return wrapper
    return decorator

# Usage
@cache_result(expiry=1800)  # 30 minutes
async def get_expensive_data(symbol: str):
    # Expensive operation
    data = await fetch_from_external_api(symbol)
    return data
```

### Database Connection Pooling
```python
# database.py
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool
import asyncpg

# Synchronous connection pool
engine = create_engine(
    settings.database_url,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    pool_recycle=3600
)

# Async connection pool
import asyncio

class AsyncDatabaseManager:
    def __init__(self):
        self.pool = None
    
    async def create_pool(self):
        self.pool = await asyncpg.create_pool(
            settings.database_url,
            min_size=10,
            max_size=20,
            command_timeout=60
        )
    
    async def execute_query(self, query: str, *args):
        async with self.pool.acquire() as connection:
            return await connection.fetch(query, *args)
    
    async def close_pool(self):
        if self.pool:
            await self.pool.close()

db_manager = AsyncDatabaseManager()

@app.on_event("startup")
async def startup():
    await db_manager.create_pool()

@app.on_event("shutdown")
async def shutdown():
    await db_manager.close_pool()
```

---

*Next: [Code Examples Repository](./07-code-examples-repository.md)*
