# OpenBB API Integration Guide

## Table of Contents
- [Overview](#overview)
- [Installation & Setup](#installation--setup)
- [Python Library Usage](#python-library-usage)
- [REST API Server](#rest-api-server)
- [Custom Backend Development](#custom-backend-development)
- [Data Provider Integration](#data-provider-integration)
- [Authentication & Security](#authentication--security)
- [Performance Optimization](#performance-optimization)
- [Troubleshooting](#troubleshooting)

## Overview

The OpenBB Platform provides multiple ways to access and integrate financial data:

1. **Python Library**: Direct programmatic access
2. **REST API Server**: HTTP-based data access
3. **Custom Backends**: Your own data integration layer
4. **CLI Interface**: Command-line data access

## Installation & Setup

### Basic Installation
```bash
# Core installation
pip install openbb

# Full installation with all extensions
pip install "openbb[all]"

# CLI installation
pip install openbb-cli
```

### Environment Setup
```python
# Set up environment variables (optional)
import os
os.environ['OPENBB_API_KEY'] = 'your_api_key'
os.environ['OPENBB_LOG_LEVEL'] = 'INFO'
```

### Verify Installation
```python
from openbb import obb
print(obb.system.python_version())
print(obb.system.build())
```

## Python Library Usage

### Basic Data Access

#### Equity Data
```python
from openbb import obb

# Historical stock prices
historical = obb.equity.price.historical(
    symbol="AAPL",
    start_date="2024-01-01",
    end_date="2024-12-31",
    provider="yfinance"
)
df = historical.to_dataframe()

# Current quote
quote = obb.equity.price.quote("AAPL")
print(quote.results[0].last_price)

# Company fundamentals
income = obb.equity.fundamental.income("AAPL", period="annual")
balance = obb.equity.fundamental.balance("AAPL")
cash_flow = obb.equity.fundamental.cash("AAPL")
```

#### Options Data
```python
# Options chains
chains = obb.derivatives.options.chains(
    symbol="AAPL",
    expiration="2024-12-20"
)

# Options volume
volume = obb.derivatives.options.volume("AAPL")
```

#### Cryptocurrency Data
```python
# Crypto historical data
crypto_hist = obb.crypto.price.historical(
    symbol="BTC-USD",
    start_date="2024-01-01"
)

# Crypto market data
market_data = obb.crypto.market.global_market_cap()
```

#### Economic Data
```python
# GDP data
gdp = obb.economy.gdp(country="US")

# Inflation data
cpi = obb.economy.cpi(country="US")

# Employment data
unemployment = obb.economy.unemployment(country="US")
```

### Advanced Usage Patterns

#### Multiple Data Sources
```python
# Compare data from different providers
yahoo_data = obb.equity.price.historical("AAPL", provider="yfinance")
alpha_data = obb.equity.price.historical("AAPL", provider="alpha_vantage")

# Combine datasets
combined_df = pd.merge(
    yahoo_data.to_dataframe(),
    alpha_data.to_dataframe(),
    on="date",
    suffixes=("_yahoo", "_alpha")
)
```

#### Batch Processing
```python
symbols = ["AAPL", "GOOGL", "MSFT", "TSLA"]
data_dict = {}

for symbol in symbols:
    try:
        data = obb.equity.price.historical(symbol)
        data_dict[symbol] = data.to_dataframe()
    except Exception as e:
        print(f"Error fetching {symbol}: {e}")
```

#### Custom Data Processing
```python
def calculate_returns(symbol, period="1y"):
    """Calculate stock returns with technical indicators"""
    data = obb.equity.price.historical(symbol, start_date=f"-{period}")
    df = data.to_dataframe()
    
    # Calculate returns
    df['daily_return'] = df['close'].pct_change()
    df['cumulative_return'] = (1 + df['daily_return']).cumprod() - 1
    
    # Add moving averages
    df['ma_20'] = df['close'].rolling(20).mean()
    df['ma_50'] = df['close'].rolling(50).mean()
    
    return df

# Usage
aapl_analysis = calculate_returns("AAPL", "2y")
```

## REST API Server

### Starting the Server
```bash
# Start with default settings
openbb-api

# Custom host and port
openbb-api --host 0.0.0.0 --port 8080

# With specific log level
openbb-api --log-level DEBUG
```

### API Endpoints Structure
```
Base URL: http://127.0.0.1:6900/api/v1/

Equity Endpoints:
- GET /equity/price/historical?symbol=AAPL&start_date=2024-01-01
- GET /equity/price/quote?symbol=AAPL
- GET /equity/fundamental/income?symbol=AAPL

Options Endpoints:
- GET /derivatives/options/chains?symbol=AAPL
- GET /derivatives/options/volume?symbol=AAPL

Crypto Endpoints:
- GET /crypto/price/historical?symbol=BTC-USD
- GET /crypto/market/global_market_cap

Economy Endpoints:
- GET /economy/gdp?country=US
- GET /economy/cpi?country=US
```

### Making API Requests

#### Using curl
```bash
# Get historical data
curl "http://127.0.0.1:6900/api/v1/equity/price/historical?symbol=AAPL&start_date=2024-01-01"

# Get current quote
curl "http://127.0.0.1:6900/api/v1/equity/price/quote?symbol=AAPL"
```

#### Using Python requests
```python
import requests

base_url = "http://127.0.0.1:6900/api/v1"

# Get historical data
response = requests.get(f"{base_url}/equity/price/historical", params={
    "symbol": "AAPL",
    "start_date": "2024-01-01",
    "provider": "yfinance"
})

data = response.json()
print(data['results'])
```

#### Using JavaScript/Node.js
```javascript
const axios = require('axios');

async function getStockData(symbol) {
    try {
        const response = await axios.get(`http://127.0.0.1:6900/api/v1/equity/price/historical`, {
            params: {
                symbol: symbol,
                start_date: '2024-01-01'
            }
        });
        return response.data.results;
    } catch (error) {
        console.error('Error fetching data:', error);
    }
}

// Usage
getStockData('AAPL').then(data => console.log(data));
```

## Custom Backend Development

### Basic FastAPI Backend
```python
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import json
from pathlib import Path
from typing import Optional
import pandas as pd

app = FastAPI(
    title="Custom Financial Backend",
    description="Custom backend for OpenBB Workspace",
    version="1.0.0"
)

# Enable CORS for OpenBB Workspace
origins = ["https://pro.openbb.co"]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Widget configuration
@app.get("/widgets.json")
def get_widgets():
    return {
        "stock_data": {
            "name": "Stock Data",
            "description": "Historical stock price data",
            "type": "table",
            "endpoint": "stock_data",
            "category": "Equity",
            "params": [
                {
                    "type": "text",
                    "paramName": "symbol",
                    "value": "AAPL",
                    "label": "Stock Symbol"
                }
            ]
        }
    }

# Data endpoint
@app.get("/stock_data")
def stock_data(symbol: str = "AAPL"):
    try:
        # Your data logic here
        from openbb import obb
        data = obb.equity.price.historical(symbol)
        df = data.to_dataframe()
        return df.to_dict('records')
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
```

### Widget Registration System
```python
# Widget registry decorator
WIDGETS = {}

def register_widget(config):
    def decorator(func):
        endpoint = config.get("endpoint")
        if endpoint:
            config["id"] = endpoint
            WIDGETS[endpoint] = config
        return func
    return decorator

@app.get("/widgets.json")
def get_widgets():
    return WIDGETS

# Register widgets using decorator
@register_widget({
    "name": "Portfolio Performance",
    "description": "Track portfolio performance over time",
    "type": "chart",
    "endpoint": "portfolio_performance",
    "category": "Portfolio"
})
@app.get("/portfolio_performance")
def portfolio_performance():
    # Implementation here
    pass
```

### Database Integration
```python
import sqlite3
import pandas as pd

@register_widget({
    "name": "Custom Database Data",
    "description": "Data from custom database",
    "type": "table",
    "endpoint": "db_data"
})
@app.get("/db_data")
def db_data(query: Optional[str] = None):
    conn = sqlite3.connect('financial_data.db')
    
    if query:
        df = pd.read_sql_query(query, conn)
    else:
        df = pd.read_sql_query("SELECT * FROM stock_prices LIMIT 100", conn)
    
    conn.close()
    return df.to_dict('records')
```

## Data Provider Integration

### Adding Custom Data Sources
```python
class CustomDataProvider:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.customdata.com"
    
    def get_stock_data(self, symbol: str, start_date: str):
        # Custom API call logic
        response = requests.get(f"{self.base_url}/stocks/{symbol}", 
                              headers={"Authorization": f"Bearer {self.api_key}"},
                              params={"start_date": start_date})
        return response.json()

# Use in endpoint
@app.get("/custom_stock_data")
def custom_stock_data(symbol: str, start_date: str):
    provider = CustomDataProvider(api_key="your_key")
    data = provider.get_stock_data(symbol, start_date)
    return data
```

### Data Transformation Pipeline
```python
def transform_data(raw_data, data_type="stock"):
    """Transform raw data to OpenBB format"""
    if data_type == "stock":
        df = pd.DataFrame(raw_data)
        # Standardize column names
        column_mapping = {
            'timestamp': 'date',
            'open_price': 'open',
            'high_price': 'high',
            'low_price': 'low',
            'close_price': 'close',
            'trade_volume': 'volume'
        }
        df = df.rename(columns=column_mapping)
        df['date'] = pd.to_datetime(df['date'])
        return df.to_dict('records')
    
    return raw_data
```

## Authentication & Security

### API Key Authentication
```python
from fastapi import Header, HTTPException

async def verify_api_key(x_api_key: str = Header(None)):
    if x_api_key != "your_secret_key":
        raise HTTPException(status_code=401, detail="Invalid API Key")
    return x_api_key

@app.get("/protected_data")
async def protected_data(api_key: str = Depends(verify_api_key)):
    return {"message": "This is protected data"}
```

### JWT Token Authentication
```python
from jose import JWTError, jwt
from datetime import datetime, timedelta

SECRET_KEY = "your_secret_key"
ALGORITHM = "HS256"

def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(hours=24)
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

async def verify_token(token: str = Header(None)):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")
```

## Performance Optimization

### Caching Implementation
```python
from functools import lru_cache
import redis

# In-memory caching
@lru_cache(maxsize=100)
def get_cached_stock_data(symbol: str, date: str):
    # Expensive data operation
    return fetch_stock_data(symbol, date)

# Redis caching
redis_client = redis.Redis(host='localhost', port=6379, db=0)

def get_cached_data(key: str, fetch_func, expiry=3600):
    cached = redis_client.get(key)
    if cached:
        return json.loads(cached)
    
    data = fetch_func()
    redis_client.setex(key, expiry, json.dumps(data))
    return data
```

### Async Data Processing
```python
import asyncio
import aiohttp

async def fetch_multiple_stocks(symbols: list):
    async with aiohttp.ClientSession() as session:
        tasks = []
        for symbol in symbols:
            task = fetch_stock_async(session, symbol)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        return dict(zip(symbols, results))

async def fetch_stock_async(session, symbol):
    url = f"https://api.example.com/stock/{symbol}"
    async with session.get(url) as response:
        return await response.json()
```

## Troubleshooting

### Common Issues

#### 1. CORS Errors
```python
# Ensure CORS is properly configured
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://pro.openbb.co"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)
```

#### 2. Data Format Issues
```python
# Ensure data is JSON serializable
import json
from datetime import datetime

def json_serializer(obj):
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Object {obj} is not JSON serializable")

# Use in response
return json.dumps(data, default=json_serializer)
```

#### 3. Rate Limiting
```python
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(429, _rate_limit_exceeded_handler)

@app.get("/limited_endpoint")
@limiter.limit("10/minute")
async def limited_endpoint(request: Request):
    return {"message": "This endpoint is rate limited"}
```

### Debugging Tips

1. **Enable Debug Logging**:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. **Test Endpoints Locally**:
```bash
curl -X GET "http://127.0.0.1:8000/widgets.json" -H "accept: application/json"
```

3. **Validate JSON Response**:
```python
import json
try:
    json.dumps(your_data)
except TypeError as e:
    print(f"JSON serialization error: {e}")
```

---

*Next: [Widget Development Manual](./03-widget-development-manual.md)*
