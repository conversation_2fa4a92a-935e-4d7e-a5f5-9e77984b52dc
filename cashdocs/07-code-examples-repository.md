# Code Examples Repository

## Table of Contents
- [Quick Start Templates](#quick-start-templates)
- [Widget Examples](#widget-examples)
- [Database Integration Examples](#database-integration-examples)
- [AI Agent Examples](#ai-agent-examples)
- [Real-time Data Examples](#real-time-data-examples)
- [Advanced Use Cases](#advanced-use-cases)
- [Testing Examples](#testing-examples)
- [Deployment Examples](#deployment-examples)

## Quick Start Templates

### Basic Backend Template
```python
# basic_backend.py
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import json
from pathlib import Path
from typing import Optional
import pandas as pd
from openbb import obb

# Widget registry
WIDGETS = {}

def register_widget(config):
    def decorator(func):
        endpoint = config.get("endpoint")
        if endpoint:
            config["id"] = endpoint
            WIDGETS[endpoint] = config
        return func
    return decorator

# FastAPI app
app = FastAPI(
    title="OpenBB Custom Backend",
    description="Custom backend for OpenBB Workspace",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://pro.openbb.co"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {"message": "OpenBB Custom Backend"}

@app.get("/widgets.json")
def get_widgets():
    return WIDGETS

@app.get("/health")
def health_check():
    return {"status": "healthy"}

# Example widget
@register_widget({
    "name": "Stock Price",
    "description": "Current stock price",
    "type": "metric",
    "endpoint": "stock_price",
    "category": "Equity",
    "params": [
        {
            "type": "text",
            "paramName": "symbol",
            "value": "AAPL",
            "label": "Stock Symbol"
        }
    ]
})
@app.get("/stock_price")
def stock_price(symbol: str = "AAPL"):
    try:
        quote = obb.equity.price.quote(symbol)
        price = quote.results[0].last_price
        
        return {
            "value": price,
            "title": f"{symbol} Current Price",
            "currency": "USD"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### Environment Setup Template
```python
# .env
DATABASE_URL=postgresql://user:password@localhost:5432/openbb_db
REDIS_URL=redis://localhost:6379
SECRET_KEY=your-secret-key-here
OPENBB_API_KEY=your-openbb-api-key
ALPHA_VANTAGE_KEY=your-alpha-vantage-key
LOG_LEVEL=INFO

# requirements.txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
openbb==4.4.4
pandas==2.1.3
numpy==1.25.2
redis==5.0.1
psycopg2-binary==2.9.9
sqlalchemy==2.0.23
pydantic==2.5.0
python-jose[cryptography]==3.3.0
python-multipart==0.0.6
slowapi==0.1.9
plotly==5.17.0
```

## Widget Examples

### Complete Stock Analysis Widget
```python
@register_widget({
    "name": "Stock Analysis Dashboard",
    "description": "Comprehensive stock analysis with technical indicators",
    "type": "table",
    "endpoint": "stock_analysis",
    "category": "Equity",
    "gridData": {"w": 24, "h": 16},
    "data": {
        "table": {
            "enableCharts": True,
            "chartView": {
                "enabled": True,
                "chartType": "line",
                "cellRangeCols": {
                    "line": ["date", "close", "ma_20", "ma_50"],
                    "column": ["date", "volume"]
                }
            },
            "columnsDefs": [
                {
                    "field": "date",
                    "headerName": "Date",
                    "chartDataType": "category",
                    "cellDataType": "date"
                },
                {
                    "field": "close",
                    "headerName": "Close Price",
                    "chartDataType": "series",
                    "cellDataType": "number",
                    "formatterFn": "none",
                    "prefix": "$"
                },
                {
                    "field": "ma_20",
                    "headerName": "MA 20",
                    "chartDataType": "series",
                    "cellDataType": "number",
                    "formatterFn": "none",
                    "prefix": "$"
                },
                {
                    "field": "volume",
                    "headerName": "Volume",
                    "chartDataType": "series",
                    "cellDataType": "number",
                    "formatterFn": "int"
                },
                {
                    "field": "rsi",
                    "headerName": "RSI",
                    "cellDataType": "number",
                    "formatterFn": "none",
                    "renderFn": "columnColor",
                    "renderFnParams": {
                        "colorRules": [
                            {"condition": "gt", "value": 70, "color": "red"},
                            {"condition": "lt", "value": 30, "color": "green"},
                            {"condition": "between", "value": [30, 70], "color": "yellow"}
                        ]
                    }
                }
            ]
        }
    },
    "params": [
        {
            "type": "text",
            "paramName": "symbol",
            "value": "AAPL",
            "label": "Stock Symbol"
        },
        {
            "type": "date",
            "paramName": "start_date",
            "value": "$currentDate-1y",
            "label": "Start Date"
        },
        {
            "type": "text",
            "paramName": "period",
            "value": "1d",
            "label": "Period",
            "options": [
                {"label": "1 Day", "value": "1d"},
                {"label": "1 Week", "value": "1wk"},
                {"label": "1 Month", "value": "1mo"}
            ]
        }
    ]
})
@app.get("/stock_analysis")
def stock_analysis(symbol: str = "AAPL", start_date: str = None, period: str = "1d"):
    try:
        # Get stock data
        data = obb.equity.price.historical(symbol, start_date=start_date, interval=period)
        df = data.to_dataframe()
        
        # Calculate technical indicators
        df['ma_20'] = df['close'].rolling(window=20).mean()
        df['ma_50'] = df['close'].rolling(window=50).mean()
        df['rsi'] = calculate_rsi(df['close'])
        df['macd'], df['macd_signal'] = calculate_macd(df['close'])
        
        # Add trading signals
        df['signal'] = 'HOLD'
        df.loc[(df['close'] > df['ma_20']) & (df['rsi'] < 70), 'signal'] = 'BUY'
        df.loc[(df['close'] < df['ma_20']) & (df['rsi'] > 30), 'signal'] = 'SELL'
        
        return df.to_dict('records')
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

def calculate_rsi(prices, window=14):
    """Calculate RSI indicator"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def calculate_macd(prices, fast=12, slow=26, signal=9):
    """Calculate MACD indicator"""
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd = ema_fast - ema_slow
    macd_signal = macd.ewm(span=signal).mean()
    return macd, macd_signal
```

### Portfolio Tracking Widget
```python
@register_widget({
    "name": "Portfolio Tracker",
    "description": "Track portfolio performance and allocation",
    "type": "table",
    "endpoint": "portfolio_tracker",
    "category": "Portfolio",
    "data": {
        "table": {
            "enableCharts": True,
            "chartView": {
                "enabled": True,
                "chartType": "pie"
            },
            "columnsDefs": [
                {
                    "field": "symbol",
                    "headerName": "Symbol",
                    "pinned": "left"
                },
                {
                    "field": "shares",
                    "headerName": "Shares",
                    "cellDataType": "number",
                    "formatterFn": "int"
                },
                {
                    "field": "current_price",
                    "headerName": "Current Price",
                    "cellDataType": "number",
                    "prefix": "$"
                },
                {
                    "field": "market_value",
                    "headerName": "Market Value",
                    "cellDataType": "number",
                    "prefix": "$",
                    "chartDataType": "series"
                },
                {
                    "field": "day_change",
                    "headerName": "Day Change",
                    "cellDataType": "number",
                    "renderFn": "greenRed",
                    "formatterFn": "percent"
                },
                {
                    "field": "total_return",
                    "headerName": "Total Return",
                    "cellDataType": "number",
                    "renderFn": "greenRed",
                    "formatterFn": "percent"
                }
            ]
        }
    }
})
@app.get("/portfolio_tracker")
def portfolio_tracker():
    # Sample portfolio data
    portfolio = [
        {"symbol": "AAPL", "shares": 100, "cost_basis": 150.00},
        {"symbol": "GOOGL", "shares": 50, "cost_basis": 2800.00},
        {"symbol": "MSFT", "shares": 75, "cost_basis": 300.00},
        {"symbol": "TSLA", "shares": 25, "cost_basis": 800.00}
    ]
    
    portfolio_data = []
    
    for holding in portfolio:
        try:
            # Get current price
            quote = obb.equity.price.quote(holding["symbol"])
            current_price = quote.results[0].last_price
            change_percent = quote.results[0].change_percent
            
            # Calculate metrics
            market_value = holding["shares"] * current_price
            cost_basis_total = holding["shares"] * holding["cost_basis"]
            total_return = (market_value - cost_basis_total) / cost_basis_total
            
            portfolio_data.append({
                "symbol": holding["symbol"],
                "shares": holding["shares"],
                "current_price": current_price,
                "market_value": market_value,
                "day_change": change_percent / 100,
                "total_return": total_return,
                "cost_basis": holding["cost_basis"]
            })
            
        except Exception as e:
            print(f"Error fetching data for {holding['symbol']}: {e}")
    
    return portfolio_data
```

### Economic Calendar Widget
```python
@register_widget({
    "name": "Economic Calendar",
    "description": "Upcoming economic events and indicators",
    "type": "table",
    "endpoint": "economic_calendar",
    "category": "Economy",
    "data": {
        "table": {
            "columnsDefs": [
                {
                    "field": "date",
                    "headerName": "Date",
                    "cellDataType": "date",
                    "pinned": "left"
                },
                {
                    "field": "time",
                    "headerName": "Time",
                    "cellDataType": "text"
                },
                {
                    "field": "event",
                    "headerName": "Event",
                    "cellDataType": "text",
                    "width": 300
                },
                {
                    "field": "importance",
                    "headerName": "Importance",
                    "cellDataType": "text",
                    "renderFn": "columnColor",
                    "renderFnParams": {
                        "colorRules": [
                            {"condition": "eq", "value": "High", "color": "red"},
                            {"condition": "eq", "value": "Medium", "color": "orange"},
                            {"condition": "eq", "value": "Low", "color": "green"}
                        ]
                    }
                },
                {
                    "field": "previous",
                    "headerName": "Previous",
                    "cellDataType": "text"
                },
                {
                    "field": "forecast",
                    "headerName": "Forecast",
                    "cellDataType": "text"
                },
                {
                    "field": "actual",
                    "headerName": "Actual",
                    "cellDataType": "text",
                    "renderFn": "greenRed"
                }
            ]
        }
    }
})
@app.get("/economic_calendar")
def economic_calendar():
    # Sample economic calendar data
    # In production, this would come from an economic data API
    calendar_data = [
        {
            "date": "2024-01-15",
            "time": "08:30",
            "event": "Consumer Price Index (CPI)",
            "importance": "High",
            "previous": "3.2%",
            "forecast": "3.1%",
            "actual": "3.0%"
        },
        {
            "date": "2024-01-16",
            "time": "09:15",
            "event": "Industrial Production",
            "importance": "Medium",
            "previous": "0.2%",
            "forecast": "0.3%",
            "actual": ""
        },
        {
            "date": "2024-01-17",
            "time": "10:00",
            "event": "Housing Starts",
            "importance": "Low",
            "previous": "1.35M",
            "forecast": "1.38M",
            "actual": ""
        },
        {
            "date": "2024-01-18",
            "time": "14:00",
            "event": "Federal Reserve Interest Rate Decision",
            "importance": "High",
            "previous": "5.25%",
            "forecast": "5.25%",
            "actual": ""
        }
    ]
    
    return calendar_data
```

## Database Integration Examples

### PostgreSQL Stock Data Example
```python
import psycopg2
import pandas as pd
from sqlalchemy import create_engine

class StockDataManager:
    def __init__(self, connection_string):
        self.engine = create_engine(connection_string)
    
    def create_tables(self):
        """Create database tables"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS stock_prices (
            id SERIAL PRIMARY KEY,
            symbol VARCHAR(10) NOT NULL,
            date DATE NOT NULL,
            open DECIMAL(10,2),
            high DECIMAL(10,2),
            low DECIMAL(10,2),
            close DECIMAL(10,2),
            volume BIGINT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, date)
        );
        
        CREATE INDEX IF NOT EXISTS idx_stock_symbol_date ON stock_prices(symbol, date);
        """
        
        with self.engine.connect() as conn:
            conn.execute(create_table_sql)
    
    def insert_stock_data(self, symbol: str, data: pd.DataFrame):
        """Insert stock data into database"""
        data['symbol'] = symbol
        data.to_sql('stock_prices', self.engine, if_exists='append', index=False)
    
    def get_stock_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """Get stock data from database"""
        query = """
        SELECT date, open, high, low, close, volume
        FROM stock_prices
        WHERE symbol = %(symbol)s
        AND date BETWEEN %(start_date)s AND %(end_date)s
        ORDER BY date
        """
        
        return pd.read_sql(query, self.engine, params={
            'symbol': symbol,
            'start_date': start_date,
            'end_date': end_date
        })

# Usage in widget
@register_widget({
    "name": "Database Stock Data",
    "description": "Stock data from PostgreSQL database",
    "type": "chart",
    "endpoint": "db_stock_data"
})
@app.get("/db_stock_data")
def db_stock_data(symbol: str = "AAPL", start_date: str = None, end_date: str = None):
    db_manager = StockDataManager(settings.database_url)
    
    if not end_date:
        end_date = datetime.now().strftime("%Y-%m-%d")
    if not start_date:
        start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
    
    df = db_manager.get_stock_data(symbol, start_date, end_date)
    
    # Create candlestick chart
    import plotly.graph_objects as go
    
    fig = go.Figure(data=go.Candlestick(
        x=df['date'],
        open=df['open'],
        high=df['high'],
        low=df['low'],
        close=df['close']
    ))
    
    fig.update_layout(
        title=f"{symbol} Stock Price",
        yaxis_title="Price ($)",
        xaxis_title="Date"
    )
    
    return fig.to_json()
```

## AI Agent Examples

### Financial Research Agent
```python
import openai
from typing import Dict, Any, List

class FinancialResearchAgent:
    def __init__(self, api_key: str):
        self.client = openai.OpenAI(api_key=api_key)
        self.model = "gpt-4"
    
    async def analyze_stock(self, symbol: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive stock analysis"""
        
        # Gather data
        stock_data = obb.equity.price.historical(symbol, start_date="-1y")
        fundamentals = obb.equity.fundamental.overview(symbol)
        
        # Build analysis context
        analysis_context = {
            "symbol": symbol,
            "price_data": stock_data.to_dict(),
            "fundamentals": fundamentals.to_dict() if fundamentals else {},
            "current_date": datetime.now().isoformat()
        }
        
        # Create analysis prompt
        prompt = f"""
        As a professional financial analyst, provide a comprehensive analysis of {symbol} based on the following data:
        
        Price Data: {analysis_context['price_data']}
        Fundamentals: {analysis_context['fundamentals']}
        
        Please provide:
        1. Current valuation assessment
        2. Technical analysis summary
        3. Fundamental strengths and weaknesses
        4. Risk factors
        5. Investment recommendation (Buy/Hold/Sell) with rationale
        
        Format your response in markdown with clear sections.
        """
        
        response = await self.client.chat.completions.create(
            model=self.model,
            messages=[
                {
                    "role": "system",
                    "content": "You are a professional equity research analyst with expertise in fundamental and technical analysis."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        )
        
        return {
            "symbol": symbol,
            "analysis": response.choices[0].message.content,
            "timestamp": datetime.now().isoformat(),
            "data_sources": ["OpenBB", "GPT-4 Analysis"]
        }

# Widget implementation
@register_widget({
    "name": "AI Stock Analysis",
    "description": "AI-powered comprehensive stock analysis",
    "type": "markdown",
    "endpoint": "ai_stock_analysis",
    "category": "AI Analysis"
})
@app.get("/ai_stock_analysis")
async def ai_stock_analysis(symbol: str = "AAPL"):
    agent = FinancialResearchAgent(api_key=settings.openai_api_key)
    result = await agent.analyze_stock(symbol, {})
    return result["analysis"]
```

## Real-time Data Examples

### WebSocket Price Feed
```python
from fastapi import WebSocket, WebSocketDisconnect
import asyncio
import json
import random

class PriceStreamManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.price_data = {}
    
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
    
    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
    
    async def broadcast_price_update(self, symbol: str, price_data: dict):
        message = json.dumps({
            "symbol": symbol,
            "timestamp": datetime.now().isoformat(),
            **price_data
        })
        
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                disconnected.append(connection)
        
        # Remove disconnected clients
        for conn in disconnected:
            self.active_connections.remove(conn)
    
    async def start_price_simulation(self, symbols: List[str]):
        """Simulate real-time price updates"""
        while True:
            for symbol in symbols:
                # Simulate price movement
                if symbol not in self.price_data:
                    self.price_data[symbol] = {"price": 100.0, "volume": 1000000}
                
                current_price = self.price_data[symbol]["price"]
                price_change = random.uniform(-0.02, 0.02)  # ±2% change
                new_price = current_price * (1 + price_change)
                
                volume_change = random.randint(-100000, 100000)
                new_volume = max(0, self.price_data[symbol]["volume"] + volume_change)
                
                price_update = {
                    "price": round(new_price, 2),
                    "change": round((new_price - current_price), 2),
                    "change_percent": round(price_change * 100, 2),
                    "volume": new_volume
                }
                
                self.price_data[symbol] = {
                    "price": new_price,
                    "volume": new_volume
                }
                
                await self.broadcast_price_update(symbol, price_update)
            
            await asyncio.sleep(1)  # Update every second

price_manager = PriceStreamManager()

@app.websocket("/ws/prices")
async def websocket_prices(websocket: WebSocket):
    await price_manager.connect(websocket)
    try:
        while True:
            # Keep connection alive
            await websocket.receive_text()
    except WebSocketDisconnect:
        price_manager.disconnect(websocket)

# Start price simulation on startup
@app.on_event("startup")
async def startup_event():
    symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN"]
    asyncio.create_task(price_manager.start_price_simulation(symbols))

# Live grid widget
@register_widget({
    "name": "Live Stock Prices",
    "description": "Real-time stock price updates",
    "type": "live_grid",
    "endpoint": "live_stock_prices",
    "wsEndpoint": "ws/prices",
    "data": {
        "wsRowIdColumn": "symbol",
        "table": {
            "columnsDefs": [
                {
                    "field": "symbol",
                    "headerName": "Symbol",
                    "enableCellChangeWs": False
                },
                {
                    "field": "price",
                    "headerName": "Price",
                    "enableCellChangeWs": True,
                    "renderFn": "showCellChange",
                    "prefix": "$"
                },
                {
                    "field": "change",
                    "headerName": "Change",
                    "enableCellChangeWs": True,
                    "renderFn": "greenRed",
                    "prefix": "$"
                },
                {
                    "field": "change_percent",
                    "headerName": "Change %",
                    "enableCellChangeWs": True,
                    "renderFn": "greenRed",
                    "suffix": "%"
                }
            ]
        }
    }
})
@app.get("/live_stock_prices")
def live_stock_prices():
    # Return initial data
    return [
        {"symbol": "AAPL", "price": 150.25, "change": 1.25, "change_percent": 0.84},
        {"symbol": "GOOGL", "price": 2800.50, "change": -15.30, "change_percent": -0.54},
        {"symbol": "MSFT", "price": 380.75, "change": 2.10, "change_percent": 0.55},
        {"symbol": "TSLA", "price": 245.80, "change": -3.20, "change_percent": -1.28}
    ]
```

## Testing Examples

### Widget Testing Framework
```python
# test_widgets.py
import pytest
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)

class TestWidgets:
    def test_widgets_json_endpoint(self):
        """Test widgets.json endpoint"""
        response = client.get("/widgets.json")
        assert response.status_code == 200
        
        widgets = response.json()
        assert isinstance(widgets, dict)
        
        # Check required fields for each widget
        for widget_id, widget_config in widgets.items():
            assert "name" in widget_config
            assert "description" in widget_config
            assert "endpoint" in widget_config
            assert "type" in widget_config
    
    def test_stock_price_widget(self):
        """Test stock price widget"""
        response = client.get("/stock_price?symbol=AAPL")
        assert response.status_code == 200
        
        data = response.json()
        assert "value" in data
        assert "title" in data
        assert isinstance(data["value"], (int, float))
    
    def test_stock_analysis_widget(self):
        """Test stock analysis widget"""
        response = client.get("/stock_analysis?symbol=AAPL&start_date=2024-01-01")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        
        if data:  # If data is returned
            first_record = data[0]
            required_fields = ["date", "close", "volume"]
            for field in required_fields:
                assert field in first_record
    
    def test_invalid_symbol(self):
        """Test handling of invalid stock symbol"""
        response = client.get("/stock_price?symbol=INVALID")
        assert response.status_code == 400
    
    def test_health_check(self):
        """Test health check endpoint"""
        response = client.get("/health")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"

# Performance testing
@pytest.mark.performance
class TestPerformance:
    def test_widget_response_time(self):
        """Test widget response time"""
        import time
        
        start_time = time.time()
        response = client.get("/stock_price?symbol=AAPL")
        end_time = time.time()
        
        assert response.status_code == 200
        assert (end_time - start_time) < 5.0  # Should respond within 5 seconds
    
    def test_concurrent_requests(self):
        """Test handling of concurrent requests"""
        import concurrent.futures
        import threading
        
        def make_request():
            return client.get("/stock_price?symbol=AAPL")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(10)]
            results = [future.result() for future in futures]
        
        # All requests should succeed
        for result in results:
            assert result.status_code == 200

# Run tests
if __name__ == "__main__":
    pytest.main([__file__, "-v"])
```

---

*This completes the comprehensive OpenBB Platform documentation knowledge base. Each document provides detailed information, practical examples, and best practices for mastering the OpenBB ecosystem.*
