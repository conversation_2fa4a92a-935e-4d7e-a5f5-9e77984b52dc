# Troubleshooting Guide

## Common Issues and Solutions

### Installation Issues

#### 1. OpenBB Installation Fails
**Problem**: `pip install openbb` fails with dependency errors

**Solutions**:
```bash
# Update pip and setuptools
pip install --upgrade pip setuptools wheel

# Install with specific Python version
python3.11 -m pip install openbb

# Install without cache
pip install --no-cache-dir openbb

# Install with verbose output to see errors
pip install -v openbb
```

#### 2. Missing Dependencies
**Problem**: Import errors for specific modules

**Solutions**:
```bash
# Install all extensions
pip install "openbb[all]"

# Install specific extensions
pip install "openbb[charting]"
pip install "openbb[optimization]"

# Check installed packages
pip list | grep openbb
```

### API Server Issues

#### 3. API Server Won't Start
**Problem**: `openbb-api` command fails or server doesn't start

**Solutions**:
```bash
# Check if port is in use
lsof -i :6900

# Start on different port
openbb-api --port 8080

# Check Python path
which python
which openbb-api

# Start with debug logging
openbb-api --log-level DEBUG
```

#### 4. CORS Errors in Browser
**Problem**: Cross-origin request blocked when connecting to OpenBB Workspace

**Solutions**:
```python
# Ensure CORS is properly configured
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://pro.openbb.co"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# For development, allow all origins (NOT for production)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### Widget Configuration Issues

#### 5. Widget Not Appearing in OpenBB Workspace
**Problem**: Widget defined but not showing up

**Checklist**:
1. Verify `widgets.json` endpoint returns valid JSON
2. Check widget configuration syntax
3. Ensure endpoint exists and returns data
4. Verify CORS headers

**Debug Steps**:
```bash
# Test widgets.json endpoint
curl http://localhost:8000/widgets.json

# Test widget endpoint
curl http://localhost:8000/your_widget_endpoint

# Check JSON validity
python -m json.tool widgets.json
```

#### 6. Widget Data Not Loading
**Problem**: Widget appears but shows no data or errors

**Solutions**:
```python
# Add error handling to endpoints
@app.get("/widget_endpoint")
def widget_endpoint():
    try:
        # Your data logic
        data = fetch_data()
        return data
    except Exception as e:
        # Log the error
        print(f"Error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Validate data format
def validate_data(data):
    if not isinstance(data, (list, dict)):
        raise ValueError("Data must be list or dict")
    return data
```

#### 7. Chart Widget Not Rendering
**Problem**: Chart widget shows empty or broken visualization

**Solutions**:
```python
# Ensure Plotly figure is properly formatted
import plotly.graph_objects as go

fig = go.Figure()
fig.add_trace(go.Scatter(x=[1, 2, 3], y=[4, 5, 6]))

# Return as JSON string
return fig.to_json()

# For Highcharts, return proper config
return {
    "chart": {"type": "line"},
    "series": [{"data": [1, 2, 3]}]
}
```

### Database Connection Issues

#### 8. Database Connection Fails
**Problem**: Cannot connect to database

**Solutions**:
```python
# Check connection string format
# PostgreSQL
DATABASE_URL = "postgresql://user:password@host:port/database"

# MySQL
DATABASE_URL = "mysql://user:password@host:port/database"

# SQLite
DATABASE_URL = "sqlite:///path/to/database.db"

# Test connection
from sqlalchemy import create_engine
engine = create_engine(DATABASE_URL)
try:
    with engine.connect() as conn:
        result = conn.execute("SELECT 1")
        print("Connection successful")
except Exception as e:
    print(f"Connection failed: {e}")
```

#### 9. SQL Query Errors
**Problem**: Database queries failing

**Solutions**:
```python
# Use parameterized queries
query = "SELECT * FROM stocks WHERE symbol = %(symbol)s"
params = {"symbol": "AAPL"}

# Handle SQL exceptions
try:
    df = pd.read_sql(query, engine, params=params)
except Exception as e:
    print(f"SQL Error: {e}")
    # Return empty DataFrame or error response
    return pd.DataFrame()
```

### Performance Issues

#### 10. Slow Widget Loading
**Problem**: Widgets take too long to load

**Solutions**:
```python
# Implement caching
import redis
from functools import wraps

redis_client = redis.Redis()

def cache_result(expiry=3600):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args))}"
            cached = redis_client.get(cache_key)
            if cached:
                return json.loads(cached)
            
            result = func(*args, **kwargs)
            redis_client.setex(cache_key, expiry, json.dumps(result))
            return result
        return wrapper
    return decorator

@cache_result(expiry=1800)  # 30 minutes
def expensive_data_fetch():
    # Expensive operation
    pass
```

#### 11. Memory Issues
**Problem**: High memory usage or out-of-memory errors

**Solutions**:
```python
# Use pagination for large datasets
@app.get("/large_dataset")
def large_dataset(limit: int = 1000, offset: int = 0):
    query = f"SELECT * FROM large_table LIMIT {limit} OFFSET {offset}"
    return pd.read_sql(query, engine)

# Process data in chunks
def process_large_file(file_path, chunk_size=10000):
    for chunk in pd.read_csv(file_path, chunksize=chunk_size):
        # Process chunk
        yield process_chunk(chunk)
```

### Authentication Issues

#### 12. API Key Authentication Fails
**Problem**: Authentication errors when accessing protected endpoints

**Solutions**:
```python
# Verify API key format
from fastapi import HTTPException, Depends
from fastapi.security import HTTPBearer

security = HTTPBearer()

def verify_api_key(credentials = Depends(security)):
    if credentials.credentials != "your_api_key":
        raise HTTPException(status_code=401, detail="Invalid API key")
    return credentials.credentials

# Check environment variables
import os
api_key = os.getenv("API_KEY")
if not api_key:
    raise ValueError("API_KEY environment variable not set")
```

### Data Format Issues

#### 13. JSON Serialization Errors
**Problem**: Cannot serialize data to JSON

**Solutions**:
```python
import json
from datetime import datetime
import numpy as np
import pandas as pd

def json_serializer(obj):
    """Custom JSON serializer for special types"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif pd.isna(obj):
        return None
    raise TypeError(f"Object {obj} is not JSON serializable")

# Use custom serializer
return json.dumps(data, default=json_serializer)

# Or handle in pandas
df = df.replace({np.nan: None})  # Replace NaN with None
return df.to_dict('records')
```

#### 14. Date Format Issues
**Problem**: Date formatting inconsistencies

**Solutions**:
```python
import pandas as pd
from datetime import datetime

# Standardize date formats
def standardize_dates(df, date_columns):
    for col in date_columns:
        df[col] = pd.to_datetime(df[col]).dt.strftime('%Y-%m-%d')
    return df

# Handle timezone issues
df['date'] = pd.to_datetime(df['date']).dt.tz_localize(None)
```

### WebSocket Issues

#### 15. WebSocket Connection Fails
**Problem**: Real-time widgets not updating

**Solutions**:
```python
from fastapi import WebSocket, WebSocketDisconnect
import asyncio
import json

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    try:
        while True:
            # Send data
            data = {"message": "test"}
            await websocket.send_text(json.dumps(data))
            await asyncio.sleep(1)
    except WebSocketDisconnect:
        print("Client disconnected")
    except Exception as e:
        print(f"WebSocket error: {e}")
        await websocket.close()
```

### Deployment Issues

#### 16. Docker Container Issues
**Problem**: Application fails in Docker container

**Solutions**:
```dockerfile
# Use specific Python version
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1
```

#### 17. Environment Variable Issues
**Problem**: Configuration not loading properly

**Solutions**:
```python
# Use python-dotenv for local development
from dotenv import load_dotenv
import os

load_dotenv()

# Validate required environment variables
required_vars = ["DATABASE_URL", "SECRET_KEY", "API_KEY"]
missing_vars = [var for var in required_vars if not os.getenv(var)]

if missing_vars:
    raise ValueError(f"Missing environment variables: {missing_vars}")
```

## Debugging Tools

### 1. Enable Debug Logging
```python
import logging

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Add to FastAPI
import uvicorn
uvicorn.run(app, host="0.0.0.0", port=8000, log_level="debug")
```

### 2. Request/Response Logging
```python
from fastapi import Request
import time

@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    
    print(f"{request.method} {request.url} - {response.status_code} - {process_time:.2f}s")
    return response
```

### 3. Health Check Endpoint
```python
@app.get("/health")
def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.get("/ready")
def readiness_check():
    # Check database connection, external APIs, etc.
    try:
        # Test database
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        
        return {"status": "ready"}
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Not ready: {e}")
```

## Getting Help

### 1. Check Logs
- Application logs: Check console output or log files
- System logs: Check system logs for resource issues
- Database logs: Check database server logs

### 2. Test Endpoints
```bash
# Test basic connectivity
curl http://localhost:8000/health

# Test widgets endpoint
curl http://localhost:8000/widgets.json

# Test specific widget
curl "http://localhost:8000/widget_endpoint?param=value"
```

### 3. Validate Configuration
```python
# Validate widgets.json
import json

try:
    with open('widgets.json', 'r') as f:
        widgets = json.load(f)
    print("widgets.json is valid")
except json.JSONDecodeError as e:
    print(f"Invalid JSON: {e}")
```

### 4. Community Resources
- OpenBB Discord: https://discord.com/invite/xPHTuHCmuV
- GitHub Issues: https://github.com/OpenBB-finance/OpenBB/issues
- Documentation: https://docs.openbb.co
- Stack Overflow: Tag questions with `openbb`

### 5. Professional Support
For enterprise users:
- Email: <EMAIL>
- Enterprise support portal
- Dedicated Slack channels
