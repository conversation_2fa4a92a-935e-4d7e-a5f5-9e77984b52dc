# Best Practices Guide

## Development Best Practices

### 1. Code Organization

#### Project Structure
```
openbb-backend/
├── main.py                 # FastAPI application entry point
├── config.py              # Configuration settings
├── models/                # Data models and schemas
│   ├── __init__.py
│   ├── stock.py
│   └── portfolio.py
├── services/              # Business logic
│   ├── __init__.py
│   ├── data_service.py
│   └── analysis_service.py
├── widgets/               # Widget definitions
│   ├── __init__.py
│   ├── equity_widgets.py
│   └── portfolio_widgets.py
├── utils/                 # Utility functions
│   ├── __init__.py
│   ├── cache.py
│   └── validators.py
├── tests/                 # Test files
│   ├── test_widgets.py
│   └── test_services.py
├── requirements.txt       # Dependencies
├── Dockerfile            # Container configuration
└── docker-compose.yml    # Multi-service setup
```

#### Modular Widget Design
```python
# widgets/equity_widgets.py
from ..utils.registry import register_widget
from ..services.data_service import DataService

class EquityWidgets:
    def __init__(self, data_service: DataService):
        self.data_service = data_service
    
    @register_widget({
        "name": "Stock Price",
        "description": "Current stock price display",
        "type": "metric",
        "endpoint": "stock_price",
        "category": "Equity"
    })
    def stock_price(self, symbol: str = "AAPL"):
        return self.data_service.get_current_price(symbol)
```

### 2. Configuration Management

#### Environment-Based Configuration
```python
# config.py
from pydantic import BaseSettings
from typing import List, Optional

class Settings(BaseSettings):
    # Application
    app_name: str = "OpenBB Custom Backend"
    debug: bool = False
    
    # Server
    host: str = "0.0.0.0"
    port: int = 8000
    
    # Database
    database_url: str
    redis_url: str = "redis://localhost:6379"
    
    # External APIs
    openbb_api_key: Optional[str] = None
    alpha_vantage_key: Optional[str] = None
    
    # Security
    secret_key: str
    cors_origins: List[str] = ["https://pro.openbb.co"]
    
    # Performance
    cache_ttl: int = 3600
    max_connections: int = 100
    
    class Config:
        env_file = ".env"
        case_sensitive = False

settings = Settings()
```

#### Multiple Environment Support
```python
# config/environments.py
from .base import BaseSettings

class DevelopmentSettings(BaseSettings):
    debug: bool = True
    cors_origins: List[str] = ["*"]
    log_level: str = "DEBUG"

class ProductionSettings(BaseSettings):
    debug: bool = False
    cors_origins: List[str] = ["https://pro.openbb.co"]
    log_level: str = "INFO"
    
class TestingSettings(BaseSettings):
    database_url: str = "sqlite:///test.db"
    redis_url: str = "redis://localhost:6379/1"

def get_settings():
    env = os.getenv("ENVIRONMENT", "development")
    if env == "production":
        return ProductionSettings()
    elif env == "testing":
        return TestingSettings()
    return DevelopmentSettings()
```

### 3. Error Handling

#### Comprehensive Error Handling
```python
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
import logging

logger = logging.getLogger(__name__)

class CustomException(Exception):
    def __init__(self, message: str, status_code: int = 500):
        self.message = message
        self.status_code = status_code

@app.exception_handler(CustomException)
async def custom_exception_handler(request: Request, exc: CustomException):
    logger.error(f"Custom error: {exc.message}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.message, "type": "custom_error"}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unexpected error: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error", "type": "server_error"}
    )

# Widget-level error handling
def safe_widget_execution(func):
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValueError as e:
            raise HTTPException(status_code=400, detail=f"Invalid input: {e}")
        except ConnectionError as e:
            raise HTTPException(status_code=503, detail=f"Service unavailable: {e}")
        except Exception as e:
            logger.error(f"Widget error in {func.__name__}: {e}")
            raise HTTPException(status_code=500, detail="Widget execution failed")
    return wrapper
```

### 4. Data Validation

#### Input Validation with Pydantic
```python
from pydantic import BaseModel, validator, Field
from typing import Optional, List
from datetime import datetime, date

class StockRequest(BaseModel):
    symbol: str = Field(..., min_length=1, max_length=10, description="Stock symbol")
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    period: str = Field("1y", regex="^(1d|5d|1mo|3mo|6mo|1y|2y|5y|10y)$")
    
    @validator('symbol')
    def symbol_uppercase(cls, v):
        return v.upper().strip()
    
    @validator('end_date')
    def end_date_not_future(cls, v):
        if v and v > date.today():
            raise ValueError('End date cannot be in the future')
        return v
    
    @validator('end_date')
    def end_after_start(cls, v, values):
        if v and 'start_date' in values and values['start_date']:
            if v <= values['start_date']:
                raise ValueError('End date must be after start date')
        return v

class PortfolioRequest(BaseModel):
    holdings: List[dict] = Field(..., min_items=1, max_items=100)
    benchmark: Optional[str] = "SPY"
    
    @validator('holdings')
    def validate_holdings(cls, v):
        for holding in v:
            if 'symbol' not in holding or 'shares' not in holding:
                raise ValueError('Each holding must have symbol and shares')
            if holding['shares'] <= 0:
                raise ValueError('Shares must be positive')
        return v
```

#### Response Validation
```python
from typing import Union

class StockResponse(BaseModel):
    symbol: str
    price: float
    change: float
    change_percent: float
    volume: int
    timestamp: datetime

class ErrorResponse(BaseModel):
    error: str
    message: str
    timestamp: datetime

@app.get("/stock_price", response_model=Union[StockResponse, ErrorResponse])
def get_stock_price(request: StockRequest):
    # Implementation
    pass
```

### 5. Performance Optimization

#### Caching Strategy
```python
import redis
import json
import hashlib
from functools import wraps
from typing import Any, Callable

class CacheManager:
    def __init__(self, redis_url: str):
        self.redis_client = redis.from_url(redis_url)
    
    def cache_key(self, func_name: str, *args, **kwargs) -> str:
        """Generate cache key from function name and arguments"""
        key_data = f"{func_name}:{args}:{sorted(kwargs.items())}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, key: str) -> Any:
        """Get cached value"""
        cached = self.redis_client.get(key)
        return json.loads(cached) if cached else None
    
    def set(self, key: str, value: Any, ttl: int = 3600):
        """Set cached value with TTL"""
        self.redis_client.setex(key, ttl, json.dumps(value, default=str))
    
    def invalidate_pattern(self, pattern: str):
        """Invalidate cache keys matching pattern"""
        keys = self.redis_client.keys(pattern)
        if keys:
            self.redis_client.delete(*keys)

cache_manager = CacheManager(settings.redis_url)

def cached(ttl: int = 3600, key_prefix: str = ""):
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache_key = f"{key_prefix}:{cache_manager.cache_key(func.__name__, *args, **kwargs)}"
            
            # Try cache first
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function
            result = func(*args, **kwargs)
            
            # Cache result
            cache_manager.set(cache_key, result, ttl)
            return result
        return wrapper
    return decorator

# Usage
@cached(ttl=1800, key_prefix="stock_data")
def get_stock_data(symbol: str):
    # Expensive operation
    return fetch_from_api(symbol)
```

#### Database Optimization
```python
from sqlalchemy import create_engine, text
from sqlalchemy.pool import QueuePool
import pandas as pd

class DatabaseManager:
    def __init__(self, database_url: str):
        self.engine = create_engine(
            database_url,
            poolclass=QueuePool,
            pool_size=20,
            max_overflow=30,
            pool_pre_ping=True,
            pool_recycle=3600,
            echo=False  # Set to True for SQL debugging
        )
    
    def execute_query(self, query: str, params: dict = None) -> pd.DataFrame:
        """Execute query with connection pooling"""
        with self.engine.connect() as conn:
            return pd.read_sql(text(query), conn, params=params)
    
    def execute_batch(self, queries: List[str]) -> List[pd.DataFrame]:
        """Execute multiple queries in single connection"""
        results = []
        with self.engine.connect() as conn:
            for query in queries:
                results.append(pd.read_sql(text(query), conn))
        return results

# Optimized queries
def get_stock_data_optimized(symbol: str, start_date: str, end_date: str):
    query = """
    SELECT date, open, high, low, close, volume
    FROM stock_prices 
    WHERE symbol = :symbol 
    AND date BETWEEN :start_date AND :end_date
    ORDER BY date
    LIMIT 10000  -- Prevent excessive data
    """
    
    params = {
        "symbol": symbol,
        "start_date": start_date,
        "end_date": end_date
    }
    
    return db_manager.execute_query(query, params)
```

### 6. Security Best Practices

#### Authentication & Authorization
```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import jwt
from datetime import datetime, timedelta

security = HTTPBearer()

class AuthManager:
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
        self.algorithm = "HS256"
    
    def create_token(self, user_id: str, permissions: List[str] = None) -> str:
        payload = {
            "sub": user_id,
            "permissions": permissions or [],
            "exp": datetime.utcnow() + timedelta(hours=24),
            "iat": datetime.utcnow()
        }
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def verify_token(self, credentials: HTTPAuthorizationCredentials = Depends(security)):
        try:
            payload = jwt.decode(
                credentials.credentials,
                self.secret_key,
                algorithms=[self.algorithm]
            )
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token expired"
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )

auth_manager = AuthManager(settings.secret_key)

def require_permission(permission: str):
    def dependency(token_data: dict = Depends(auth_manager.verify_token)):
        if permission not in token_data.get("permissions", []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        return token_data
    return dependency

# Protected endpoint
@app.get("/admin/data")
def admin_data(user: dict = Depends(require_permission("admin"))):
    return {"data": "sensitive_admin_data"}
```

#### Input Sanitization
```python
import re
from typing import Any

class InputSanitizer:
    @staticmethod
    def sanitize_symbol(symbol: str) -> str:
        """Sanitize stock symbol input"""
        if not symbol:
            raise ValueError("Symbol cannot be empty")
        
        # Remove non-alphanumeric characters
        sanitized = re.sub(r'[^A-Za-z0-9.-]', '', symbol.upper())
        
        if len(sanitized) > 10:
            raise ValueError("Symbol too long")
        
        return sanitized
    
    @staticmethod
    def sanitize_sql_input(value: Any) -> Any:
        """Basic SQL injection prevention"""
        if isinstance(value, str):
            # Remove potentially dangerous characters
            dangerous_chars = [';', '--', '/*', '*/', 'xp_', 'sp_']
            for char in dangerous_chars:
                if char in value.lower():
                    raise ValueError(f"Invalid character sequence: {char}")
        return value

# Usage in endpoints
@app.get("/stock_data")
def stock_data(symbol: str):
    symbol = InputSanitizer.sanitize_symbol(symbol)
    # Continue with sanitized input
```

### 7. Testing Best Practices

#### Comprehensive Test Suite
```python
import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch
import pandas as pd

class TestStockWidgets:
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    @pytest.fixture
    def mock_data_service(self):
        mock = Mock()
        mock.get_stock_data.return_value = pd.DataFrame({
            'date': ['2024-01-01', '2024-01-02'],
            'close': [150.0, 152.0],
            'volume': [1000000, 1100000]
        })
        return mock
    
    def test_stock_price_success(self, client):
        response = client.get("/stock_price?symbol=AAPL")
        assert response.status_code == 200
        
        data = response.json()
        assert "value" in data
        assert "title" in data
        assert isinstance(data["value"], (int, float))
    
    def test_stock_price_invalid_symbol(self, client):
        response = client.get("/stock_price?symbol=INVALID123")
        assert response.status_code == 400
    
    @patch('services.data_service.DataService')
    def test_stock_analysis_with_mock(self, mock_service, client):
        mock_service.return_value.get_stock_data.return_value = pd.DataFrame({
            'date': ['2024-01-01'],
            'close': [150.0]
        })
        
        response = client.get("/stock_analysis?symbol=AAPL")
        assert response.status_code == 200
    
    @pytest.mark.performance
    def test_response_time(self, client):
        import time
        start = time.time()
        response = client.get("/stock_price?symbol=AAPL")
        duration = time.time() - start
        
        assert response.status_code == 200
        assert duration < 5.0  # Should respond within 5 seconds

# Integration tests
class TestIntegration:
    def test_full_widget_flow(self, client):
        # Test widgets.json
        widgets_response = client.get("/widgets.json")
        assert widgets_response.status_code == 200
        
        widgets = widgets_response.json()
        assert "stock_price" in widgets
        
        # Test widget endpoint
        widget_response = client.get("/stock_price?symbol=AAPL")
        assert widget_response.status_code == 200
```

### 8. Monitoring & Observability

#### Structured Logging
```python
import logging
import json
from datetime import datetime
from typing import Any, Dict

class StructuredLogger:
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def log(self, level: str, message: str, **kwargs):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": level,
            "message": message,
            **kwargs
        }
        
        if level == "ERROR":
            self.logger.error(json.dumps(log_entry))
        elif level == "WARNING":
            self.logger.warning(json.dumps(log_entry))
        else:
            self.logger.info(json.dumps(log_entry))
    
    def info(self, message: str, **kwargs):
        self.log("INFO", message, **kwargs)
    
    def error(self, message: str, **kwargs):
        self.log("ERROR", message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        self.log("WARNING", message, **kwargs)

logger = StructuredLogger("openbb_backend")

# Usage
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    duration = time.time() - start_time
    
    logger.info(
        "Request processed",
        method=request.method,
        url=str(request.url),
        status_code=response.status_code,
        duration=duration,
        user_agent=request.headers.get("user-agent")
    )
    
    return response
```

#### Health Checks & Metrics
```python
from fastapi import status
import psutil
import time

class HealthChecker:
    def __init__(self, db_manager, cache_manager):
        self.db_manager = db_manager
        self.cache_manager = cache_manager
    
    def check_database(self) -> Dict[str, Any]:
        try:
            start = time.time()
            with self.db_manager.engine.connect() as conn:
                conn.execute("SELECT 1")
            duration = time.time() - start
            
            return {
                "status": "healthy",
                "response_time": duration,
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def check_cache(self) -> Dict[str, Any]:
        try:
            start = time.time()
            self.cache_manager.redis_client.ping()
            duration = time.time() - start
            
            return {
                "status": "healthy",
                "response_time": duration,
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def get_system_metrics(self) -> Dict[str, Any]:
        return {
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent,
            "timestamp": datetime.utcnow().isoformat()
        }

health_checker = HealthChecker(db_manager, cache_manager)

@app.get("/health")
def health_check():
    db_health = health_checker.check_database()
    cache_health = health_checker.check_cache()
    system_metrics = health_checker.get_system_metrics()
    
    overall_status = "healthy"
    if db_health["status"] != "healthy" or cache_health["status"] != "healthy":
        overall_status = "unhealthy"
    
    return {
        "status": overall_status,
        "database": db_health,
        "cache": cache_health,
        "system": system_metrics,
        "timestamp": datetime.utcnow().isoformat()
    }
```

### 9. Documentation Best Practices

#### API Documentation
```python
from fastapi import FastAPI
from pydantic import BaseModel

app = FastAPI(
    title="OpenBB Custom Backend",
    description="Custom backend for OpenBB Workspace with financial data widgets",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

class StockPriceResponse(BaseModel):
    value: float
    title: str
    currency: str
    change: float
    change_percent: float
    
    class Config:
        schema_extra = {
            "example": {
                "value": 150.25,
                "title": "AAPL Current Price",
                "currency": "USD",
                "change": 2.15,
                "change_percent": 1.45
            }
        }

@app.get(
    "/stock_price",
    response_model=StockPriceResponse,
    summary="Get current stock price",
    description="Retrieve the current stock price for a given symbol",
    tags=["Stock Data"]
)
def get_stock_price(
    symbol: str = Query(..., description="Stock ticker symbol", example="AAPL")
):
    """
    Get current stock price for a symbol.
    
    - **symbol**: Stock ticker symbol (e.g., AAPL, GOOGL)
    
    Returns current price, change, and percentage change.
    """
    # Implementation
    pass
```

### 10. Deployment Best Practices

#### Production Checklist
```python
# production_checklist.py
import os
from typing import List

class ProductionChecker:
    def __init__(self):
        self.checks = []
    
    def check_environment_variables(self) -> bool:
        required_vars = [
            "DATABASE_URL",
            "SECRET_KEY", 
            "REDIS_URL"
        ]
        
        missing = [var for var in required_vars if not os.getenv(var)]
        if missing:
            self.checks.append(f"Missing environment variables: {missing}")
            return False
        return True
    
    def check_security_settings(self) -> bool:
        issues = []
        
        if settings.debug:
            issues.append("Debug mode is enabled")
        
        if "*" in settings.cors_origins:
            issues.append("CORS allows all origins")
        
        if len(settings.secret_key) < 32:
            issues.append("Secret key is too short")
        
        if issues:
            self.checks.append(f"Security issues: {issues}")
            return False
        return True
    
    def run_all_checks(self) -> bool:
        all_passed = True
        
        all_passed &= self.check_environment_variables()
        all_passed &= self.check_security_settings()
        
        if not all_passed:
            print("Production readiness issues found:")
            for check in self.checks:
                print(f"- {check}")
        
        return all_passed

# Run on startup
@app.on_event("startup")
async def startup_checks():
    if os.getenv("ENVIRONMENT") == "production":
        checker = ProductionChecker()
        if not checker.run_all_checks():
            raise RuntimeError("Production readiness checks failed")
```

These best practices ensure your OpenBB backend is robust, secure, performant, and maintainable in production environments.
