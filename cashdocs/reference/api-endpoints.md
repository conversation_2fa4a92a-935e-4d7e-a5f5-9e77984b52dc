# OpenBB API Endpoints Reference

## Base URL Structure
```
Base URL: http://127.0.0.1:6900/api/v1/
```

## Equity Endpoints

### Price Data
```
GET /equity/price/historical
Parameters:
- symbol: string (required) - Stock ticker symbol
- start_date: string (optional) - Start date (YYYY-MM-DD)
- end_date: string (optional) - End date (YYYY-MM-DD)
- interval: string (optional) - Data interval (1d, 1wk, 1mo)
- provider: string (optional) - Data provider

Example: /equity/price/historical?symbol=AAPL&start_date=2024-01-01&interval=1d
```

```
GET /equity/price/quote
Parameters:
- symbol: string (required) - Stock ticker symbol
- provider: string (optional) - Data provider

Example: /equity/price/quote?symbol=AAPL
```

```
GET /equity/price/performance
Parameters:
- symbol: string (required) - Stock ticker symbol
- provider: string (optional) - Data provider

Example: /equity/price/performance?symbol=AAPL
```

### Fundamental Data
```
GET /equity/fundamental/income
Parameters:
- symbol: string (required) - Stock ticker symbol
- period: string (optional) - annual, quarter
- limit: integer (optional) - Number of periods
- provider: string (optional) - Data provider

Example: /equity/fundamental/income?symbol=AAPL&period=annual&limit=5
```

```
GET /equity/fundamental/balance
Parameters:
- symbol: string (required) - Stock ticker symbol
- period: string (optional) - annual, quarter
- limit: integer (optional) - Number of periods
- provider: string (optional) - Data provider

Example: /equity/fundamental/balance?symbol=AAPL&period=annual
```

```
GET /equity/fundamental/cash
Parameters:
- symbol: string (required) - Stock ticker symbol
- period: string (optional) - annual, quarter
- limit: integer (optional) - Number of periods
- provider: string (optional) - Data provider

Example: /equity/fundamental/cash?symbol=AAPL&period=annual
```

```
GET /equity/fundamental/overview
Parameters:
- symbol: string (required) - Stock ticker symbol
- provider: string (optional) - Data provider

Example: /equity/fundamental/overview?symbol=AAPL
```

```
GET /equity/fundamental/ratios
Parameters:
- symbol: string (required) - Stock ticker symbol
- period: string (optional) - annual, quarter
- limit: integer (optional) - Number of periods
- provider: string (optional) - Data provider

Example: /equity/fundamental/ratios?symbol=AAPL&period=annual
```

### Estimates & Analysis
```
GET /equity/estimates/consensus
Parameters:
- symbol: string (required) - Stock ticker symbol
- provider: string (optional) - Data provider

Example: /equity/estimates/consensus?symbol=AAPL
```

```
GET /equity/estimates/price_target
Parameters:
- symbol: string (required) - Stock ticker symbol
- provider: string (optional) - Data provider

Example: /equity/estimates/price_target?symbol=AAPL
```

### Ownership Data
```
GET /equity/ownership/institutional
Parameters:
- symbol: string (required) - Stock ticker symbol
- provider: string (optional) - Data provider

Example: /equity/ownership/institutional?symbol=AAPL
```

```
GET /equity/ownership/insider_trading
Parameters:
- symbol: string (required) - Stock ticker symbol
- limit: integer (optional) - Number of records
- provider: string (optional) - Data provider

Example: /equity/ownership/insider_trading?symbol=AAPL&limit=50
```

## Options/Derivatives Endpoints

### Options Chains
```
GET /derivatives/options/chains
Parameters:
- symbol: string (required) - Underlying symbol
- expiration: string (optional) - Expiration date (YYYY-MM-DD)
- provider: string (optional) - Data provider

Example: /derivatives/options/chains?symbol=AAPL&expiration=2024-12-20
```

```
GET /derivatives/options/volume
Parameters:
- symbol: string (required) - Underlying symbol
- provider: string (optional) - Data provider

Example: /derivatives/options/volume?symbol=AAPL
```

## Cryptocurrency Endpoints

### Crypto Price Data
```
GET /crypto/price/historical
Parameters:
- symbol: string (required) - Crypto symbol (e.g., BTC-USD)
- start_date: string (optional) - Start date (YYYY-MM-DD)
- end_date: string (optional) - End date (YYYY-MM-DD)
- interval: string (optional) - Data interval
- provider: string (optional) - Data provider

Example: /crypto/price/historical?symbol=BTC-USD&start_date=2024-01-01
```

```
GET /crypto/price/quote
Parameters:
- symbol: string (required) - Crypto symbol
- provider: string (optional) - Data provider

Example: /crypto/price/quote?symbol=BTC-USD
```

### Crypto Market Data
```
GET /crypto/market/global_market_cap
Parameters:
- provider: string (optional) - Data provider

Example: /crypto/market/global_market_cap
```

## Economic Data Endpoints

### Economic Indicators
```
GET /economy/gdp
Parameters:
- country: string (optional) - Country code (default: US)
- provider: string (optional) - Data provider

Example: /economy/gdp?country=US
```

```
GET /economy/cpi
Parameters:
- country: string (optional) - Country code (default: US)
- provider: string (optional) - Data provider

Example: /economy/cpi?country=US
```

```
GET /economy/unemployment
Parameters:
- country: string (optional) - Country code (default: US)
- provider: string (optional) - Data provider

Example: /economy/unemployment?country=US
```

```
GET /economy/indicators
Parameters:
- indicator: string (optional) - Specific indicator
- country: string (optional) - Country code
- provider: string (optional) - Data provider

Example: /economy/indicators?indicator=inflation&country=US
```

## Fixed Income Endpoints

### Government Bonds
```
GET /fixedincome/government/yield_curve
Parameters:
- country: string (optional) - Country code (default: US)
- provider: string (optional) - Data provider

Example: /fixedincome/government/yield_curve?country=US
```

## Index/Market Data Endpoints

### Market Indices
```
GET /index/market
Parameters:
- symbol: string (optional) - Index symbol
- provider: string (optional) - Data provider

Example: /index/market?symbol=SPY
```

## Currency/Forex Endpoints

### Currency Data
```
GET /currency/price/historical
Parameters:
- symbol: string (required) - Currency pair (e.g., EURUSD)
- start_date: string (optional) - Start date
- end_date: string (optional) - End date
- provider: string (optional) - Data provider

Example: /currency/price/historical?symbol=EURUSD&start_date=2024-01-01
```

```
GET /currency/snapshots
Parameters:
- base: string (optional) - Base currency
- provider: string (optional) - Data provider

Example: /currency/snapshots?base=USD
```

## Common Parameters

### Providers
Available data providers vary by endpoint. Common providers include:
- `yfinance` - Yahoo Finance
- `alpha_vantage` - Alpha Vantage
- `fmp` - Financial Modeling Prep
- `polygon` - Polygon.io
- `intrinio` - Intrinio
- `quandl` - Quandl

### Date Formats
- All dates should be in `YYYY-MM-DD` format
- Relative dates can be used: `-1y`, `-6m`, `-30d`

### Response Format
All endpoints return JSON in the following format:
```json
{
  "results": [...],
  "provider": "provider_name",
  "warnings": [...],
  "chart": {...},
  "extra": {...}
}
```

## Error Handling

### HTTP Status Codes
- `200` - Success
- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (invalid API key)
- `404` - Not Found (invalid endpoint)
- `429` - Too Many Requests (rate limit exceeded)
- `500` - Internal Server Error

### Error Response Format
```json
{
  "detail": "Error message",
  "error": "Error type",
  "status_code": 400
}
```

## Rate Limiting

### Default Limits
- Free tier: 100 requests per minute
- Premium tier: 1000 requests per minute
- Enterprise: Custom limits

### Headers
Rate limit information is included in response headers:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## Authentication

### API Key Authentication
Include API key in headers:
```
Authorization: Bearer YOUR_API_KEY
```

Or as query parameter:
```
?api_key=YOUR_API_KEY
```

## Example Usage

### Python with requests
```python
import requests

base_url = "http://127.0.0.1:6900/api/v1"
headers = {"Authorization": "Bearer YOUR_API_KEY"}

# Get stock data
response = requests.get(
    f"{base_url}/equity/price/historical",
    params={"symbol": "AAPL", "start_date": "2024-01-01"},
    headers=headers
)

data = response.json()
print(data["results"])
```

### JavaScript/Node.js
```javascript
const axios = require('axios');

const baseURL = 'http://127.0.0.1:6900/api/v1';
const headers = {'Authorization': 'Bearer YOUR_API_KEY'};

async function getStockData(symbol) {
    try {
        const response = await axios.get(`${baseURL}/equity/price/historical`, {
            params: {symbol, start_date: '2024-01-01'},
            headers
        });
        return response.data.results;
    } catch (error) {
        console.error('Error:', error.response.data);
    }
}
```

### curl
```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     "http://127.0.0.1:6900/api/v1/equity/price/historical?symbol=AAPL&start_date=2024-01-01"
```

## Pagination

Some endpoints support pagination:
```
GET /endpoint?limit=100&offset=0
```

Parameters:
- `limit` - Number of records per page (max 1000)
- `offset` - Number of records to skip

## Data Formats

### Date/Time
- ISO 8601 format: `2024-01-01T00:00:00Z`
- Date only: `2024-01-01`

### Numbers
- Prices: Decimal format (e.g., 150.25)
- Percentages: Decimal format (e.g., 0.0525 for 5.25%)
- Large numbers: No formatting (e.g., 1000000 for 1M)

### Symbols
- Stocks: Ticker symbols (e.g., AAPL, GOOGL)
- Crypto: Pair format (e.g., BTC-USD, ETH-USD)
- Forex: Currency pairs (e.g., EURUSD, GBPJPY)
- Options: Standard format (e.g., AAPL240315C00150000)
