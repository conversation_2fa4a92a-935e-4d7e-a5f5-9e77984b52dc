# Widget Configuration Schema Reference

## Complete widgets.json Schema

This document provides the complete reference for configuring widgets in OpenBB Workspace.

### Basic Widget Structure
```json
{
  "widget_id": {
    "name": "string (required)",
    "description": "string (required)", 
    "endpoint": "string (required)",
    "type": "string (required)",
    "category": "string (optional)",
    "subCategory": "string (optional)",
    "gridData": "object (optional)",
    "data": "object (optional)",
    "params": "array (optional)",
    "source": "array (optional)",
    "refetchInterval": "number (optional)",
    "staleTime": "number (optional)",
    "runButton": "boolean (optional)"
  }
}
```

## Core Properties

### Required Properties

#### name
- **Type**: `string`
- **Description**: Display name shown to users
- **Example**: `"Stock Price Analysis"`

#### description  
- **Type**: `string`
- **Description**: Brief description for users and AI Copilot
- **Example**: `"Real-time stock price data with technical indicators"`

#### endpoint
- **Type**: `string` 
- **Description**: API endpoint path for data retrieval
- **Example**: `"stock_analysis"`

#### type
- **Type**: `string`
- **Options**: `"table"`, `"chart"`, `"markdown"`, `"metric"`, `"live_grid"`, `"newsfeed"`, `"multi_file_viewer"`, `"advanced-chart"`, `"chart-highcharts"`
- **Default**: `"table"`

### Optional Properties

#### category
- **Type**: `string`
- **Description**: Main category for organization
- **Example**: `"Equity"`, `"Options"`, `"Crypto"`

#### subCategory
- **Type**: `string`
- **Description**: Secondary category for filtering
- **Example**: `"Analysis"`, `"Fundamentals"`, `"Technical"`

#### source
- **Type**: `array of strings`
- **Description**: Data source labels
- **Example**: `["OpenBB API", "Custom Database"]`

#### runButton
- **Type**: `boolean`
- **Description**: Show run button instead of auto-refresh
- **Default**: `false`

#### refetchInterval
- **Type**: `number`
- **Description**: Auto-refresh interval in milliseconds
- **Default**: `900000` (15 minutes)
- **Minimum**: `1000`

#### staleTime
- **Type**: `number`
- **Description**: Data staleness threshold in milliseconds
- **Default**: `300000` (5 minutes)

## Grid Layout Configuration

### gridData Object
```json
{
  "gridData": {
    "w": 12,        // Width (1-40)
    "h": 8,         // Height (1-100)
    "minW": 6,      // Minimum width
    "minH": 4,      // Minimum height
    "maxW": 24,     // Maximum width
    "maxH": 20      // Maximum height
  }
}
```

## Data Configuration

### data Object Structure
```json
{
  "data": {
    "dataKey": "string (optional)",
    "wsRowIdColumn": "string (optional)",
    "table": {
      "enableCharts": "boolean",
      "showAll": "boolean", 
      "chartView": "object",
      "columnsDefs": "array"
    }
  }
}
```

### Table Configuration

#### enableCharts
- **Type**: `boolean`
- **Description**: Enable chart visualization for table data
- **Default**: `false`

#### showAll
- **Type**: `boolean`
- **Description**: Display all available data
- **Default**: `true`

#### chartView Object
```json
{
  "chartView": {
    "enabled": true,
    "chartType": "line",
    "cellRangeCols": {
      "line": ["date", "price", "volume"],
      "column": ["symbol", "market_cap"]
    },
    "ignoreCellRange": false
  }
}
```

**Chart Types**: `column`, `groupedColumn`, `stackedColumn`, `normalizedColumn`, `bar`, `groupedBar`, `stackedBar`, `normalizedBar`, `line`, `scatter`, `bubble`, `pie`, `donut`, `doughnut`, `area`, `stackedArea`, `normalizedArea`, `histogram`, `radarLine`, `radarArea`, `nightingale`, `radialColumn`, `radialBar`, `sunburst`, `rangeBar`, `rangeArea`, `boxPlot`, `treemap`, `heatmap`, `waterfall`

### Column Definitions

#### columnsDefs Array
```json
{
  "columnsDefs": [
    {
      "field": "string (required)",
      "headerName": "string (required)",
      "chartDataType": "string",
      "cellDataType": "string", 
      "formatterFn": "string",
      "renderFn": "string|array",
      "renderFnParams": "object",
      "width": "number",
      "maxWidth": "number",
      "minWidth": "number",
      "hide": "boolean",
      "pinned": "string",
      "headerTooltip": "string",
      "prefix": "string",
      "suffix": "string",
      "enableCellChangeWs": "boolean"
    }
  ]
}
```

#### Column Properties

**field** (required)
- **Type**: `string`
- **Description**: Field name from JSON data
- **Example**: `"close_price"`

**headerName** (required)
- **Type**: `string`
- **Description**: Display name for column header
- **Example**: `"Close Price"`

**chartDataType**
- **Type**: `string`
- **Options**: `"category"`, `"series"`, `"time"`, `"excluded"`
- **Description**: How data is treated in charts

**cellDataType**
- **Type**: `string`
- **Options**: `"text"`, `"number"`, `"boolean"`, `"date"`, `"dateString"`, `"object"`
- **Description**: Data type for proper formatting

**formatterFn**
- **Type**: `string`
- **Options**: `"int"`, `"none"`, `"percent"`, `"normalized"`, `"normalizedPercent"`, `"dateToYear"`
- **Description**: Number formatting function

**renderFn**
- **Type**: `string` or `array`
- **Options**: `"greenRed"`, `"titleCase"`, `"hoverCard"`, `"cellOnClick"`, `"columnColor"`, `"showCellChange"`
- **Description**: Cell rendering function

**width**, **maxWidth**, **minWidth**
- **Type**: `number`
- **Description**: Column width in pixels

**hide**
- **Type**: `boolean`
- **Description**: Hide column from table
- **Default**: `false`

**pinned**
- **Type**: `string`
- **Options**: `"left"`, `"right"`
- **Description**: Pin column to side

**enableCellChangeWs**
- **Type**: `boolean`
- **Description**: Enable WebSocket cell updates (live_grid only)
- **Default**: `true`

## Parameter Configuration

### params Array
```json
{
  "params": [
    {
      "type": "string (required)",
      "paramName": "string (required)",
      "value": "any (required)",
      "label": "string (required)",
      "description": "string (optional)",
      "show": "boolean (optional)",
      "options": "array (optional)",
      "optionsEndpoint": "string (optional)",
      "optionsParams": "object (optional)",
      "multiple": "boolean (optional)",
      "multiSelect": "boolean (optional)",
      "style": "object (optional)",
      "roles": "array (optional)"
    }
  ]
}
```

### Parameter Types

#### text
```json
{
  "type": "text",
  "paramName": "symbol",
  "value": "AAPL",
  "label": "Stock Symbol",
  "description": "Enter stock ticker symbol"
}
```

#### date
```json
{
  "type": "date", 
  "paramName": "start_date",
  "value": "$currentDate-1y",
  "label": "Start Date"
}
```

**Dynamic Dates**: Use `$currentDate` with modifiers:
- `h` - hours
- `d` - days  
- `w` - weeks
- `M` - months
- `y` - years

Examples: `$currentDate-1w`, `$currentDate+1d`

#### dropdown
```json
{
  "type": "text",
  "paramName": "period",
  "value": "1y",
  "label": "Time Period",
  "options": [
    {"label": "1 Month", "value": "1m"},
    {"label": "1 Year", "value": "1y"}
  ]
}
```

#### boolean
```json
{
  "type": "boolean",
  "paramName": "include_volume",
  "value": true,
  "label": "Include Volume"
}
```

#### number
```json
{
  "type": "number",
  "paramName": "days",
  "value": 30,
  "label": "Number of Days"
}
```

### Advanced Parameter Features

#### Dynamic Options
```json
{
  "type": "text",
  "paramName": "exchange",
  "optionsEndpoint": "exchanges",
  "optionsParams": {"country": "$country"}
}
```

#### Multi-Select
```json
{
  "type": "text",
  "paramName": "symbols",
  "multiSelect": true,
  "multiple": true,
  "options": [...]
}
```

## Render Function Parameters

### renderFnParams Object
```json
{
  "renderFnParams": {
    "actionType": "string",
    "groupByParamName": "string",
    "colorValueKey": "string", 
    "hoverCardData": "array",
    "colorRules": "array",
    "hoverCard": "object",
    "sendToAgent": "object"
  }
}
```

### Color Rules
```json
{
  "colorRules": [
    {"condition": "gt", "value": 50, "color": "green", "fill": true},
    {"condition": "lt", "value": 0, "color": "red", "fill": true},
    {"condition": "eq", "value": 0, "color": "gray", "fill": false}
  ]
}
```

**Conditions**: `"gt"`, `"lt"`, `"eq"`, `"gte"`, `"lte"`, `"between"`

### Hover Card
```json
{
  "hoverCard": {
    "cellField": "symbol",
    "title": "Stock Details", 
    "markdown": "### {symbol}\n- **Price**: ${price}\n- **Volume**: {volume:,}"
  }
}
```

### Send to Agent
```json
{
  "sendToAgent": {
    "markdown": "Analyze **{company}** with revenue of ${revenue}M",
    "agentId": "financial-analyst-agent"
  }
}
```

## Live Grid Configuration

### WebSocket Properties
```json
{
  "type": "live_grid",
  "wsEndpoint": "ws_endpoint_name",
  "data": {
    "wsRowIdColumn": "symbol",
    "table": {
      "columnsDefs": [
        {
          "field": "price",
          "enableCellChangeWs": true,
          "renderFn": "showCellChange"
        }
      ]
    }
  }
}
```

## Complete Example

```json
{
  "advanced_stock_widget": {
    "name": "Advanced Stock Analysis",
    "description": "Comprehensive stock analysis with technical indicators and AI insights",
    "endpoint": "advanced_stock_analysis",
    "type": "table",
    "category": "Equity",
    "subCategory": "Analysis",
    "runButton": false,
    "gridData": {
      "w": 24,
      "h": 16,
      "minW": 12,
      "minH": 8
    },
    "data": {
      "table": {
        "enableCharts": true,
        "showAll": true,
        "chartView": {
          "enabled": true,
          "chartType": "line",
          "cellRangeCols": {
            "line": ["date", "close", "ma_20", "ma_50"],
            "column": ["date", "volume"]
          }
        },
        "columnsDefs": [
          {
            "field": "date",
            "headerName": "Date",
            "chartDataType": "category",
            "cellDataType": "date",
            "pinned": "left"
          },
          {
            "field": "close",
            "headerName": "Close Price",
            "chartDataType": "series",
            "cellDataType": "number",
            "formatterFn": "none",
            "prefix": "$",
            "width": 120
          },
          {
            "field": "rsi",
            "headerName": "RSI",
            "cellDataType": "number",
            "renderFn": "columnColor",
            "renderFnParams": {
              "colorRules": [
                {"condition": "gt", "value": 70, "color": "red"},
                {"condition": "lt", "value": 30, "color": "green"}
              ]
            }
          },
          {
            "field": "signal",
            "headerName": "Signal",
            "renderFn": "cellOnClick",
            "renderFnParams": {
              "actionType": "sendToAgent",
              "sendToAgent": {
                "markdown": "Explain the {signal} signal for {symbol} on {date}"
              }
            }
          }
        ]
      }
    },
    "params": [
      {
        "type": "text",
        "paramName": "symbol",
        "value": "AAPL",
        "label": "Stock Symbol",
        "description": "Enter stock ticker symbol"
      },
      {
        "type": "date",
        "paramName": "start_date", 
        "value": "$currentDate-1y",
        "label": "Start Date"
      },
      {
        "type": "text",
        "paramName": "indicators",
        "value": "RSI,MACD",
        "label": "Technical Indicators",
        "multiSelect": true,
        "options": [
          {"label": "RSI", "value": "RSI"},
          {"label": "MACD", "value": "MACD"},
          {"label": "Bollinger Bands", "value": "BB"}
        ]
      }
    ],
    "source": ["OpenBB API", "Custom Analysis"],
    "refetchInterval": 300000,
    "staleTime": 60000
  }
}
```
