# Custom API Integrations Guide

## Adding Custom APIs to OpenBB

This guide shows how to integrate any custom API into your OpenBB backend, using Cardano ecosystem APIs as examples.

## Cardano Ecosystem Integration

### 1. Taptools API Integration

```python
# services/taptools_service.py
import requests
import pandas as pd
from typing import Dict, List, Any
import os

class TaptoolsService:
    def __init__(self):
        self.base_url = "https://api.taptools.io"
        self.api_key = os.getenv("TAPTOOLS_API_KEY")
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def get_token_info(self, policy_id: str, asset_name: str = None) -> Dict[str, Any]:
        """Get detailed token information"""
        endpoint = f"/tokens/{policy_id}"
        if asset_name:
            endpoint += f"/{asset_name}"
        
        response = requests.get(f"{self.base_url}{endpoint}", headers=self.headers)
        response.raise_for_status()
        return response.json()
    
    def get_token_price_history(self, policy_id: str, days: int = 30) -> pd.DataFrame:
        """Get token price history"""
        endpoint = f"/tokens/{policy_id}/price-history"
        params = {"days": days}
        
        response = requests.get(
            f"{self.base_url}{endpoint}", 
            headers=self.headers, 
            params=params
        )
        response.raise_for_status()
        
        data = response.json()
        df = pd.DataFrame(data["price_history"])
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        return df
    
    def get_trending_tokens(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get trending Cardano tokens"""
        endpoint = "/trending"
        params = {"limit": limit}
        
        response = requests.get(
            f"{self.base_url}{endpoint}", 
            headers=self.headers, 
            params=params
        )
        response.raise_for_status()
        return response.json()["tokens"]
    
    def get_pool_analytics(self, pool_id: str) -> Dict[str, Any]:
        """Get DEX pool analytics"""
        endpoint = f"/pools/{pool_id}/analytics"
        
        response = requests.get(f"{self.base_url}{endpoint}", headers=self.headers)
        response.raise_for_status()
        return response.json()

# Widget implementations
@register_widget({
    "name": "Cardano Token Tracker",
    "description": "Track Cardano native tokens via Taptools",
    "type": "table",
    "endpoint": "cardano_tokens",
    "category": "Cardano",
    "data": {
        "table": {
            "enableCharts": True,
            "columnsDefs": [
                {
                    "field": "name",
                    "headerName": "Token Name",
                    "pinned": "left"
                },
                {
                    "field": "price_ada",
                    "headerName": "Price (ADA)",
                    "cellDataType": "number",
                    "formatterFn": "none"
                },
                {
                    "field": "price_usd",
                    "headerName": "Price (USD)",
                    "cellDataType": "number",
                    "prefix": "$"
                },
                {
                    "field": "change_24h",
                    "headerName": "24h Change",
                    "cellDataType": "number",
                    "renderFn": "greenRed",
                    "formatterFn": "percent"
                },
                {
                    "field": "volume_24h",
                    "headerName": "24h Volume",
                    "cellDataType": "number",
                    "prefix": "$",
                    "formatterFn": "int"
                }
            ]
        }
    }
})
@app.get("/cardano_tokens")
def cardano_tokens():
    taptools = TaptoolsService()
    trending = taptools.get_trending_tokens(limit=20)
    
    token_data = []
    for token in trending:
        token_data.append({
            "name": token.get("name", "Unknown"),
            "symbol": token.get("symbol", ""),
            "price_ada": token.get("price_ada", 0),
            "price_usd": token.get("price_usd", 0),
            "change_24h": token.get("change_24h", 0) / 100,
            "volume_24h": token.get("volume_24h_usd", 0),
            "market_cap": token.get("market_cap_usd", 0)
        })
    
    return token_data
```

### 2. Blockfrost API Integration

```python
# services/blockfrost_service.py
import requests
import pandas as pd
from typing import Dict, List, Any
import os

class BlockfrostService:
    def __init__(self):
        self.base_url = "https://cardano-mainnet.blockfrost.io/api/v0"
        self.project_id = os.getenv("BLOCKFROST_PROJECT_ID")
        self.headers = {
            "project_id": self.project_id,
            "Content-Type": "application/json"
        }
    
    def get_network_info(self) -> Dict[str, Any]:
        """Get Cardano network information"""
        response = requests.get(f"{self.base_url}/network", headers=self.headers)
        response.raise_for_status()
        return response.json()
    
    def get_epoch_info(self, epoch_number: int = None) -> Dict[str, Any]:
        """Get epoch information"""
        endpoint = "/epochs/latest" if epoch_number is None else f"/epochs/{epoch_number}"
        response = requests.get(f"{self.base_url}{endpoint}", headers=self.headers)
        response.raise_for_status()
        return response.json()
    
    def get_pool_list(self, count: int = 100, page: int = 1) -> List[str]:
        """Get list of stake pools"""
        params = {"count": count, "page": page, "order": "asc"}
        response = requests.get(
            f"{self.base_url}/pools", 
            headers=self.headers, 
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    def get_pool_info(self, pool_id: str) -> Dict[str, Any]:
        """Get detailed pool information"""
        response = requests.get(
            f"{self.base_url}/pools/{pool_id}", 
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()
    
    def get_address_info(self, address: str) -> Dict[str, Any]:
        """Get address information and UTXOs"""
        response = requests.get(
            f"{self.base_url}/addresses/{address}", 
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()
    
    def get_asset_info(self, asset: str) -> Dict[str, Any]:
        """Get native asset information"""
        response = requests.get(
            f"{self.base_url}/assets/{asset}", 
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()

# Widget implementations
@register_widget({
    "name": "Cardano Network Stats",
    "description": "Real-time Cardano blockchain statistics",
    "type": "metric",
    "endpoint": "cardano_network_stats",
    "category": "Cardano"
})
@app.get("/cardano_network_stats")
def cardano_network_stats():
    blockfrost = BlockfrostService()
    network_info = blockfrost.get_network_info()
    epoch_info = blockfrost.get_epoch_info()
    
    return {
        "value": f"{network_info['supply']['circulating']:,.0f}",
        "title": "ADA Circulating Supply",
        "subtitle": f"Epoch {epoch_info['epoch']} • {epoch_info['blocks']} blocks",
        "change": f"+{network_info['supply']['locked']:,.0f} locked",
        "currency": "ADA"
    }

@register_widget({
    "name": "Cardano Stake Pools",
    "description": "Top performing Cardano stake pools",
    "type": "table",
    "endpoint": "cardano_stake_pools",
    "category": "Cardano"
})
@app.get("/cardano_stake_pools")
def cardano_stake_pools():
    blockfrost = BlockfrostService()
    pool_list = blockfrost.get_pool_list(count=20)
    
    pool_data = []
    for pool_id in pool_list[:10]:  # Top 10 pools
        try:
            pool_info = blockfrost.get_pool_info(pool_id)
            pool_data.append({
                "pool_id": pool_id[:10] + "...",
                "ticker": pool_info.get("ticker", "N/A"),
                "name": pool_info.get("name", "Unknown"),
                "margin_cost": pool_info.get("margin_cost", 0),
                "fixed_cost": pool_info.get("fixed_cost", 0) / 1000000,  # Convert to ADA
                "pledge": pool_info.get("pledge", 0) / 1000000,  # Convert to ADA
                "active_stake": pool_info.get("active_stake", 0) / 1000000,  # Convert to ADA
                "blocks_minted": pool_info.get("blocks_minted", 0)
            })
        except Exception as e:
            print(f"Error fetching pool {pool_id}: {e}")
    
    return pool_data
```

## 🤖 MASTRA Agent Integration

### Custom MASTRA Agent Integration

```python
# services/mastra_agent_service.py
import requests
import json
from typing import Dict, Any, List

class MastraAgentService:
    def __init__(self):
        self.base_url = os.getenv("MASTRA_AGENT_URL", "http://localhost:3000")
        self.api_key = os.getenv("MASTRA_AGENT_API_KEY")
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    async def query_agent(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Send query to MASTRA agent"""
        payload = {
            "query": query,
            "context": context or {},
            "session_id": "openbb_session",
            "user_id": "openbb_user"
        }
        
        response = requests.post(
            f"{self.base_url}/api/chat",
            headers=self.headers,
            json=payload
        )
        response.raise_for_status()
        return response.json()
    
    async def get_agent_capabilities(self) -> List[str]:
        """Get list of agent capabilities"""
        response = requests.get(
            f"{self.base_url}/api/capabilities",
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()["capabilities"]
    
    async def analyze_portfolio(self, portfolio_data: List[Dict]) -> Dict[str, Any]:
        """Use MASTRA agent to analyze portfolio"""
        query = f"""
        Analyze this portfolio and provide insights:
        {json.dumps(portfolio_data, indent=2)}
        
        Please provide:
        1. Risk assessment
        2. Diversification analysis
        3. Performance insights
        4. Recommendations
        """
        
        return await self.query_agent(query, {"type": "portfolio_analysis"})
    
    async def market_sentiment_analysis(self, symbols: List[str]) -> Dict[str, Any]:
        """Get market sentiment analysis from MASTRA agent"""
        query = f"""
        Provide market sentiment analysis for these symbols: {', '.join(symbols)}
        
        Include:
        1. Overall market sentiment
        2. Individual stock sentiment
        3. Key factors driving sentiment
        4. Short-term outlook
        """
        
        return await self.query_agent(query, {"type": "sentiment_analysis", "symbols": symbols})

# Widget implementations
@register_widget({
    "name": "MASTRA AI Portfolio Analyst",
    "description": "AI-powered portfolio analysis using MASTRA agent",
    "type": "markdown",
    "endpoint": "mastra_portfolio_analysis",
    "category": "AI Analysis",
    "params": [
        {
            "type": "text",
            "paramName": "portfolio_json",
            "value": '[{"symbol": "AAPL", "shares": 100}, {"symbol": "GOOGL", "shares": 50}]',
            "label": "Portfolio JSON",
            "description": "Enter portfolio as JSON array"
        }
    ]
})
@app.get("/mastra_portfolio_analysis")
async def mastra_portfolio_analysis(portfolio_json: str):
    try:
        portfolio_data = json.loads(portfolio_json)
        mastra = MastraAgentService()
        analysis = await mastra.analyze_portfolio(portfolio_data)
        
        return f"""
# MASTRA AI Portfolio Analysis

{analysis.get('response', 'Analysis not available')}

## Key Insights
{analysis.get('insights', 'No insights available')}

## Recommendations
{analysis.get('recommendations', 'No recommendations available')}

---
*Analysis generated by MASTRA AI Agent*
        """
    except Exception as e:
        return f"Error: {str(e)}"

@register_widget({
    "name": "MASTRA Market Sentiment",
    "description": "Real-time market sentiment analysis via MASTRA",
    "type": "table",
    "endpoint": "mastra_market_sentiment",
    "category": "AI Analysis"
})
@app.get("/mastra_market_sentiment")
async def mastra_market_sentiment():
    symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "NVDA"]
    mastra = MastraAgentService()
    
    try:
        analysis = await mastra.market_sentiment_analysis(symbols)
        
        # Parse the AI response into structured data
        sentiment_data = []
        for symbol in symbols:
            sentiment_data.append({
                "symbol": symbol,
                "sentiment": "Positive",  # Parse from AI response
                "confidence": 0.85,      # Parse from AI response
                "key_factors": "Strong earnings, market optimism",
                "recommendation": "Hold"
            })
        
        return sentiment_data
    except Exception as e:
        return [{"error": str(e)}]
```

## 🌟 The Incredible Extent of Possibilities

### **1. Multi-Chain Crypto Integration**
```python
# You can integrate ANY blockchain
- Ethereum (Etherscan, Alchemy, Infura)
- Solana (Solana RPC, Helius)
- Polygon (Polygon API)
- Avalanche (Avalanche API)
- Binance Smart Chain
- Cardano (Blockfrost, Taptools)
- Polkadot (Subscan)
- Cosmos (Mintscan)
```

### **2. Alternative Data Sources**
```python
# Social & Sentiment
- Twitter API (sentiment analysis)
- Reddit API (community sentiment)
- Discord APIs (community activity)
- Telegram APIs (group sentiment)

# Satellite & IoT Data
- Satellite imagery for commodity analysis
- Weather APIs for agricultural insights
- Shipping data for trade analysis
- Energy consumption data

# Web Scraping
- News websites
- Financial forums
- Government websites
- Corporate filings
```

### **3. Advanced AI Integration**
```python
# Multiple AI Providers
- OpenAI GPT-4/GPT-4o
- Anthropic Claude
- Google Gemini
- Local LLMs (Ollama, LM Studio)
- Custom fine-tuned models

# Specialized AI Services
- Computer vision for chart analysis
- NLP for document processing
- Time series forecasting
- Anomaly detection
- Risk modeling
```

### **4. Real-Time Data Streams**
```python
# Market Data Streams
- WebSocket feeds from exchanges
- Kafka streams for high-frequency data
- Redis pub/sub for real-time updates
- Server-sent events for live charts

# Custom Data Pipelines
- Apache Airflow for ETL
- Apache Spark for big data processing
- ClickHouse for time series storage
- InfluxDB for metrics
```

### **5. Enterprise Integrations**
```python
# Business Systems
- Salesforce CRM data
- HubSpot marketing data
- Slack for notifications
- Microsoft Teams integration
- Jira for project tracking

# Financial Systems
- QuickBooks accounting
- Stripe payment data
- PayPal transaction data
- Banking APIs (Plaid, Yodlee)
- Trading platforms (Interactive Brokers, TD Ameritrade)
```

### **6. Custom Analytics Engines**
```python
# You can build sophisticated analysis engines:

class CustomAnalyticsEngine:
    def __init__(self):
        self.ml_models = self.load_models()
        self.data_sources = self.init_data_sources()
    
    def multi_asset_correlation_analysis(self):
        # Custom correlation analysis across asset classes
        pass
    
    def risk_parity_optimization(self):
        # Custom portfolio optimization
        pass
    
    def sentiment_driven_trading_signals(self):
        # Combine sentiment with technical analysis
        pass
    
    def macro_economic_impact_modeling(self):
        # Model economic events impact on markets
        pass
```

### **7. Advanced Visualization**
```python
# Custom Chart Types
- 3D visualizations
- Interactive network graphs
- Heatmaps and treemaps
- Candlestick with custom indicators
- Real-time streaming charts
- Geographic data visualization
- Custom D3.js integrations
```

### **8. Workflow Automation**
```python
# Automated Trading Strategies
- Signal generation and execution
- Risk management automation
- Portfolio rebalancing
- Alert systems
- Report generation

# Business Process Automation
- Automated research reports
- Compliance monitoring
- Risk alerts
- Performance tracking
- Client notifications
```

## 🚀 Getting Started with Custom Integrations

### Environment Setup
```bash
# Add your custom API keys
TAPTOOLS_API_KEY=your_taptools_key
BLOCKFROST_PROJECT_ID=your_blockfrost_project_id
MASTRA_AGENT_URL=http://your-mastra-agent.com
MASTRA_AGENT_API_KEY=your_mastra_key

# Additional custom APIs
CUSTOM_API_1_KEY=your_key
CUSTOM_API_2_URL=https://api.example.com
CUSTOM_DATABASE_URL=postgresql://...
```

### Quick Integration Template
```python
# services/custom_api_service.py
class CustomAPIService:
    def __init__(self):
        self.api_key = os.getenv("YOUR_API_KEY")
        self.base_url = "https://your-api.com"
    
    def get_data(self, params):
        # Your custom API logic
        pass

# Add to your widgets
@register_widget({
    "name": "Your Custom Widget",
    "description": "Data from your custom API",
    "type": "table",
    "endpoint": "custom_data"
})
@app.get("/custom_data")
def custom_data():
    service = CustomAPIService()
    return service.get_data({})
```

The extent is truly limitless! You can integrate:
- **Any API** (REST, GraphQL, WebSocket)
- **Any database** (SQL, NoSQL, Graph, Time-series)
- **Any AI service** (OpenAI, custom models, specialized APIs)
- **Any data source** (files, streams, web scraping)
- **Any visualization** (charts, maps, 3D, custom)
- **Any workflow** (automation, alerts, reports)

The OpenBB platform becomes your unified interface for **any financial data or analysis** you can imagine!
