# Database Connectors Reference

## Table of Contents
- [Overview](#overview)
- [SQL Databases](#sql-databases)
- [NoSQL Databases](#nosql-databases)
- [Cloud Data Warehouses](#cloud-data-warehouses)
- [Time Series Databases](#time-series-databases)
- [Real-time Data Sources](#real-time-data-sources)
- [Connection Management](#connection-management)
- [Performance Optimization](#performance-optimization)
- [Security Best Practices](#security-best-practices)

## Overview

OpenBB Workspace supports integration with various database systems through custom backends. This guide provides examples and best practices for connecting different types of databases to create powerful financial data widgets.

### Supported Database Types
- **SQL Databases**: PostgreSQL, MySQL, SQLite, SQL Server
- **NoSQL Databases**: MongoDB, Redis, Cassandra
- **Cloud Warehouses**: Snowflake, BigQuery, Redshift, Databricks
- **Time Series**: InfluxDB, TimescaleDB, ClickHouse
- **Real-time**: Apache Kafka, Redis Streams, WebSockets

## SQL Databases

### PostgreSQL Integration
```python
import psycopg2
import pandas as pd
from sqlalchemy import create_engine
import os

class PostgreSQLConnector:
    def __init__(self):
        self.connection_string = os.getenv("POSTGRESQL_URL",
            "postgresql://user:password@localhost:5432/financial_db")
        self.engine = create_engine(self.connection_string)

    def execute_query(self, query: str, params: dict = None) -> pd.DataFrame:
        """Execute SQL query and return DataFrame"""
        try:
            df = pd.read_sql_query(query, self.engine, params=params)
            return df
        except Exception as e:
            raise Exception(f"Database query failed: {e}")

    def get_stock_prices(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """Get stock prices from PostgreSQL"""
        query = """
        SELECT date, open, high, low, close, volume
        FROM stock_prices
        WHERE symbol = %(symbol)s
        AND date BETWEEN %(start_date)s AND %(end_date)s
        ORDER BY date
        """

        params = {
            "symbol": symbol,
            "start_date": start_date,
            "end_date": end_date
        }

        return self.execute_query(query, params)

# Widget implementation
@register_widget({
    "name": "PostgreSQL Stock Data",
    "description": "Stock data from PostgreSQL database",
    "type": "table",
    "endpoint": "postgres_stock_data",
    "category": "Database",
    "params": [
        {
            "type": "text",
            "paramName": "symbol",
            "value": "AAPL",
            "label": "Stock Symbol"
        },
        {
            "type": "date",
            "paramName": "start_date",
            "value": "$currentDate-1y",
            "label": "Start Date"
        }
    ]
})
@app.get("/postgres_stock_data")
def postgres_stock_data(symbol: str = "AAPL", start_date: str = None, end_date: str = None):
    connector = PostgreSQLConnector()

    if not end_date:
        end_date = datetime.now().strftime("%Y-%m-%d")

    df = connector.get_stock_prices(symbol, start_date, end_date)
    return df.to_dict('records')
```

### MySQL Integration
```python
import mysql.connector
from mysql.connector import Error
import pandas as pd

class MySQLConnector:
    def __init__(self):
        self.config = {
            'host': os.getenv('MYSQL_HOST', 'localhost'),
            'database': os.getenv('MYSQL_DATABASE', 'financial_data'),
            'user': os.getenv('MYSQL_USER', 'root'),
            'password': os.getenv('MYSQL_PASSWORD', 'password'),
            'port': int(os.getenv('MYSQL_PORT', 3306))
        }

    def get_connection(self):
        """Create MySQL connection"""
        try:
            connection = mysql.connector.connect(**self.config)
            return connection
        except Error as e:
            raise Exception(f"MySQL connection failed: {e}")

    def execute_query(self, query: str, params: tuple = None) -> pd.DataFrame:
        """Execute query and return DataFrame"""
        connection = self.get_connection()

        try:
            df = pd.read_sql(query, connection, params=params)
            return df
        finally:
            connection.close()

    def get_portfolio_performance(self, portfolio_id: int) -> pd.DataFrame:
        """Get portfolio performance data"""
        query = """
        SELECT
            p.date,
            p.total_value,
            p.daily_return,
            p.cumulative_return,
            p.benchmark_return
        FROM portfolio_performance p
        WHERE p.portfolio_id = %s
        ORDER BY p.date DESC
        LIMIT 252
        """

        return self.execute_query(query, (portfolio_id,))

@register_widget({
    "name": "Portfolio Performance",
    "description": "Portfolio performance from MySQL",
    "type": "chart",
    "endpoint": "mysql_portfolio_performance"
})
@app.get("/mysql_portfolio_performance")
def mysql_portfolio_performance(portfolio_id: int = 1):
    connector = MySQLConnector()
    df = connector.get_portfolio_performance(portfolio_id)

    # Create Plotly chart
    import plotly.graph_objects as go

    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=df['date'],
        y=df['cumulative_return'],
        mode='lines',
        name='Portfolio Return'
    ))

    fig.add_trace(go.Scatter(
        x=df['date'],
        y=df['benchmark_return'],
        mode='lines',
        name='Benchmark Return'
    ))

    fig.update_layout(
        title="Portfolio vs Benchmark Performance",
        xaxis_title="Date",
        yaxis_title="Cumulative Return (%)"
    )

    return fig.to_json()
```

### SQLite Integration
```python
import sqlite3
import pandas as pd
from pathlib import Path

class SQLiteConnector:
    def __init__(self, db_path: str = "financial_data.db"):
        self.db_path = Path(db_path)
        self.init_database()

    def init_database(self):
        """Initialize database with required tables"""
        if not self.db_path.exists():
            self.create_tables()

    def get_connection(self):
        """Get SQLite connection"""
        return sqlite3.connect(self.db_path)

    def create_tables(self):
        """Create initial database schema"""
        conn = self.get_connection()

        # Create stock_prices table
        conn.execute("""
        CREATE TABLE IF NOT EXISTS stock_prices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            date DATE NOT NULL,
            open REAL,
            high REAL,
            low REAL,
            close REAL,
            volume INTEGER,
            UNIQUE(symbol, date)
        )
        """)

        # Create earnings table
        conn.execute("""
        CREATE TABLE IF NOT EXISTS earnings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            quarter TEXT NOT NULL,
            year INTEGER NOT NULL,
            revenue REAL,
            net_income REAL,
            eps REAL,
            UNIQUE(symbol, quarter, year)
        )
        """)

        conn.commit()
        conn.close()

    def execute_query(self, query: str, params: tuple = None) -> pd.DataFrame:
        """Execute query and return DataFrame"""
        conn = self.get_connection()

        try:
            df = pd.read_sql_query(query, conn, params=params)
            return df
        finally:
            conn.close()

    def get_earnings_data(self, symbol: str) -> pd.DataFrame:
        """Get earnings data for a symbol"""
        query = """
        SELECT quarter, year, revenue, net_income, eps
        FROM earnings
        WHERE symbol = ?
        ORDER BY year DESC, quarter DESC
        LIMIT 20
        """

        return self.execute_query(query, (symbol,))

@register_widget({
    "name": "Earnings History",
    "description": "Historical earnings data from SQLite",
    "type": "table",
    "endpoint": "sqlite_earnings_data"
})
@app.get("/sqlite_earnings_data")
def sqlite_earnings_data(symbol: str = "AAPL"):
    connector = SQLiteConnector()
    df = connector.get_earnings_data(symbol)
    return df.to_dict('records')
```

## NoSQL Databases

### MongoDB Integration
```python
from pymongo import MongoClient
import pandas as pd
from datetime import datetime, timedelta

class MongoDBConnector:
    def __init__(self):
        self.connection_string = os.getenv("MONGODB_URL",
            "mongodb://localhost:27017/")
        self.client = MongoClient(self.connection_string)
        self.db = self.client.financial_data

    def get_news_sentiment(self, symbol: str, days: int = 30) -> pd.DataFrame:
        """Get news sentiment data from MongoDB"""
        collection = self.db.news_sentiment

        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # Query MongoDB
        pipeline = [
            {
                "$match": {
                    "symbol": symbol,
                    "date": {"$gte": start_date, "$lte": end_date}
                }
            },
            {
                "$group": {
                    "_id": {
                        "date": {"$dateToString": {"format": "%Y-%m-%d", "date": "$date"}}
                    },
                    "avg_sentiment": {"$avg": "$sentiment_score"},
                    "article_count": {"$sum": 1},
                    "positive_count": {
                        "$sum": {"$cond": [{"$gt": ["$sentiment_score", 0.1]}, 1, 0]}
                    },
                    "negative_count": {
                        "$sum": {"$cond": [{"$lt": ["$sentiment_score", -0.1]}, 1, 0]}
                    }
                }
            },
            {"$sort": {"_id.date": 1}}
        ]

        results = list(collection.aggregate(pipeline))

        # Convert to DataFrame
        data = []
        for result in results:
            data.append({
                "date": result["_id"]["date"],
                "avg_sentiment": result["avg_sentiment"],
                "article_count": result["article_count"],
                "positive_count": result["positive_count"],
                "negative_count": result["negative_count"]
            })

        return pd.DataFrame(data)

    def get_social_mentions(self, symbol: str) -> dict:
        """Get social media mentions from MongoDB"""
        collection = self.db.social_mentions

        # Get recent mentions
        pipeline = [
            {"$match": {"symbol": symbol}},
            {
                "$group": {
                    "_id": "$platform",
                    "mention_count": {"$sum": 1},
                    "avg_sentiment": {"$avg": "$sentiment"},
                    "latest_mention": {"$max": "$timestamp"}
                }
            }
        ]

        results = list(collection.aggregate(pipeline))
        return {"social_data": results}

@register_widget({
    "name": "News Sentiment Analysis",
    "description": "News sentiment data from MongoDB",
    "type": "chart",
    "endpoint": "mongo_news_sentiment"
})
@app.get("/mongo_news_sentiment")
def mongo_news_sentiment(symbol: str = "AAPL", days: int = 30):
    connector = MongoDBConnector()
    df = connector.get_news_sentiment(symbol, days)

    # Create sentiment chart
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots

    fig = make_subplots(
        rows=2, cols=1,
        subplot_titles=('Average Sentiment', 'Article Count'),
        vertical_spacing=0.1
    )

    # Sentiment line
    fig.add_trace(
        go.Scatter(
            x=df['date'],
            y=df['avg_sentiment'],
            mode='lines+markers',
            name='Average Sentiment',
            line=dict(color='blue')
        ),
        row=1, col=1
    )

    # Article count bars
    fig.add_trace(
        go.Bar(
            x=df['date'],
            y=df['article_count'],
            name='Article Count',
            marker_color='lightblue'
        ),
        row=2, col=1
    )

    fig.update_layout(
        title=f"News Sentiment Analysis - {symbol}",
        height=600
    )

    return fig.to_json()
```

### Redis Integration
```python
import redis
import json
import pandas as pd
from typing import Dict, Any

class RedisConnector:
    def __init__(self):
        self.redis_client = redis.Redis(
            host=os.getenv('REDIS_HOST', 'localhost'),
            port=int(os.getenv('REDIS_PORT', 6379)),
            db=int(os.getenv('REDIS_DB', 0)),
            decode_responses=True
        )

    def get_real_time_prices(self, symbols: list) -> pd.DataFrame:
        """Get real-time prices from Redis"""
        data = []

        for symbol in symbols:
            price_data = self.redis_client.hgetall(f"price:{symbol}")
            if price_data:
                data.append({
                    "symbol": symbol,
                    "price": float(price_data.get("price", 0)),
                    "change": float(price_data.get("change", 0)),
                    "change_percent": float(price_data.get("change_percent", 0)),
                    "volume": int(price_data.get("volume", 0)),
                    "last_updated": price_data.get("last_updated")
                })

        return pd.DataFrame(data)

    def get_market_movers(self, limit: int = 10) -> pd.DataFrame:
        """Get top market movers from Redis sorted sets"""
        # Get top gainers
        gainers = self.redis_client.zrevrange("gainers", 0, limit-1, withscores=True)

        # Get top losers
        losers = self.redis_client.zrange("losers", 0, limit-1, withscores=True)

        data = []

        # Process gainers
        for symbol, change in gainers:
            price_data = self.redis_client.hgetall(f"price:{symbol}")
            data.append({
                "symbol": symbol,
                "type": "Gainer",
                "price": float(price_data.get("price", 0)),
                "change_percent": change,
                "volume": int(price_data.get("volume", 0))
            })

        # Process losers
        for symbol, change in losers:
            price_data = self.redis_client.hgetall(f"price:{symbol}")
            data.append({
                "symbol": symbol,
                "type": "Loser",
                "price": float(price_data.get("price", 0)),
                "change_percent": change,
                "volume": int(price_data.get("volume", 0))
            })

        return pd.DataFrame(data)

@register_widget({
    "name": "Market Movers",
    "description": "Real-time market movers from Redis",
    "type": "table",
    "endpoint": "redis_market_movers",
    "data": {
        "table": {
            "columnsDefs": [
                {
                    "field": "symbol",
                    "headerName": "Symbol",
                    "pinned": "left"
                },
                {
                    "field": "type",
                    "headerName": "Type",
                    "renderFn": "columnColor",
                    "renderFnParams": {
                        "colorRules": [
                            {"condition": "eq", "value": "Gainer", "color": "green"},
                            {"condition": "eq", "value": "Loser", "color": "red"}
                        ]
                    }
                },
                {
                    "field": "change_percent",
                    "headerName": "Change %",
                    "renderFn": "greenRed",
                    "formatterFn": "percent"
                }
            ]
        }
    }
})
@app.get("/redis_market_movers")
def redis_market_movers(limit: int = 10):
    connector = RedisConnector()
    df = connector.get_market_movers(limit)
    return df.to_dict('records')

## Cloud Data Warehouses

### Snowflake Integration
```python
import snowflake.connector
import pandas as pd
from sqlalchemy import create_engine

class SnowflakeConnector:
    def __init__(self):
        self.connection_params = {
            'user': os.getenv('SNOWFLAKE_USER'),
            'password': os.getenv('SNOWFLAKE_PASSWORD'),
            'account': os.getenv('SNOWFLAKE_ACCOUNT'),
            'warehouse': os.getenv('SNOWFLAKE_WAREHOUSE'),
            'database': os.getenv('SNOWFLAKE_DATABASE'),
            'schema': os.getenv('SNOWFLAKE_SCHEMA')
        }

    def get_connection(self):
        """Create Snowflake connection"""
        return snowflake.connector.connect(**self.connection_params)

    def execute_query(self, query: str) -> pd.DataFrame:
        """Execute query and return DataFrame"""
        conn = self.get_connection()

        try:
            cursor = conn.cursor()
            cursor.execute(query)

            # Fetch results
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()

            df = pd.DataFrame(data, columns=columns)
            return df
        finally:
            conn.close()

    def get_market_data_summary(self, date_range: int = 30) -> pd.DataFrame:
        """Get market data summary from Snowflake"""
        query = f"""
        SELECT
            DATE_TRUNC('day', trade_date) as date,
            COUNT(DISTINCT symbol) as symbols_traded,
            SUM(volume) as total_volume,
            AVG(close_price) as avg_price,
            STDDEV(close_price) as price_volatility
        FROM market_data.stock_prices
        WHERE trade_date >= DATEADD(day, -{date_range}, CURRENT_DATE())
        GROUP BY DATE_TRUNC('day', trade_date)
        ORDER BY date DESC
        """

        return self.execute_query(query)

@register_widget({
    "name": "Market Summary",
    "description": "Market data summary from Snowflake",
    "type": "chart",
    "endpoint": "snowflake_market_summary"
})
@app.get("/snowflake_market_summary")
def snowflake_market_summary(days: int = 30):
    connector = SnowflakeConnector()
    df = connector.get_market_data_summary(days)

    # Create multi-metric chart
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots

    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('Total Volume', 'Average Price', 'Symbols Traded', 'Price Volatility')
    )

    # Volume
    fig.add_trace(
        go.Scatter(x=df['date'], y=df['total_volume'], name='Volume'),
        row=1, col=1
    )

    # Average Price
    fig.add_trace(
        go.Scatter(x=df['date'], y=df['avg_price'], name='Avg Price'),
        row=1, col=2
    )

    # Symbols Traded
    fig.add_trace(
        go.Scatter(x=df['date'], y=df['symbols_traded'], name='Symbols'),
        row=2, col=1
    )

    # Volatility
    fig.add_trace(
        go.Scatter(x=df['date'], y=df['price_volatility'], name='Volatility'),
        row=2, col=2
    )

    fig.update_layout(title="Market Summary Dashboard", height=600)
    return fig.to_json()
```

### BigQuery Integration
```python
from google.cloud import bigquery
import pandas as pd

class BigQueryConnector:
    def __init__(self):
        # Initialize BigQuery client
        self.client = bigquery.Client()
        self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT')

    def execute_query(self, query: str) -> pd.DataFrame:
        """Execute BigQuery query and return DataFrame"""
        try:
            df = self.client.query(query).to_dataframe()
            return df
        except Exception as e:
            raise Exception(f"BigQuery error: {e}")

    def get_sector_performance(self, period: str = "1M") -> pd.DataFrame:
        """Get sector performance analysis"""
        query = f"""
        SELECT
            sector,
            COUNT(DISTINCT symbol) as stock_count,
            AVG(price_change_pct) as avg_return,
            STDDEV(price_change_pct) as volatility,
            SUM(market_cap) as total_market_cap
        FROM `{self.project_id}.financial_data.stock_performance`
        WHERE period = '{period}'
        GROUP BY sector
        ORDER BY avg_return DESC
        """

        return self.execute_query(query)

    def get_earnings_calendar(self, days_ahead: int = 7) -> pd.DataFrame:
        """Get upcoming earnings calendar"""
        query = f"""
        SELECT
            symbol,
            company_name,
            earnings_date,
            estimated_eps,
            previous_eps,
            sector
        FROM `{self.project_id}.financial_data.earnings_calendar`
        WHERE earnings_date BETWEEN CURRENT_DATE()
        AND DATE_ADD(CURRENT_DATE(), INTERVAL {days_ahead} DAY)
        ORDER BY earnings_date, symbol
        """

        return self.execute_query(query)

@register_widget({
    "name": "Sector Performance",
    "description": "Sector performance analysis from BigQuery",
    "type": "table",
    "endpoint": "bigquery_sector_performance",
    "data": {
        "table": {
            "enableCharts": true,
            "chartView": {
                "enabled": true,
                "chartType": "column"
            },
            "columnsDefs": [
                {
                    "field": "sector",
                    "headerName": "Sector",
                    "chartDataType": "category"
                },
                {
                    "field": "avg_return",
                    "headerName": "Avg Return (%)",
                    "chartDataType": "series",
                    "renderFn": "greenRed",
                    "formatterFn": "percent"
                },
                {
                    "field": "total_market_cap",
                    "headerName": "Market Cap",
                    "formatterFn": "int",
                    "prefix": "$",
                    "suffix": "B"
                }
            ]
        }
    }
})
@app.get("/bigquery_sector_performance")
def bigquery_sector_performance(period: str = "1M"):
    connector = BigQueryConnector()
    df = connector.get_sector_performance(period)
    return df.to_dict('records')
```

## Time Series Databases

### InfluxDB Integration
```python
from influxdb_client import InfluxDBClient
import pandas as pd

class InfluxDBConnector:
    def __init__(self):
        self.client = InfluxDBClient(
            url=os.getenv('INFLUXDB_URL', 'http://localhost:8086'),
            token=os.getenv('INFLUXDB_TOKEN'),
            org=os.getenv('INFLUXDB_ORG')
        )
        self.query_api = self.client.query_api()

    def get_real_time_metrics(self, symbol: str, timeframe: str = "1h") -> pd.DataFrame:
        """Get real-time trading metrics from InfluxDB"""
        query = f'''
        from(bucket: "financial_data")
        |> range(start: -{timeframe})
        |> filter(fn: (r) => r["_measurement"] == "trading_metrics")
        |> filter(fn: (r) => r["symbol"] == "{symbol}")
        |> filter(fn: (r) => r["_field"] == "price" or r["_field"] == "volume" or r["_field"] == "spread")
        |> aggregateWindow(every: 1m, fn: mean, createEmpty: false)
        |> yield(name: "mean")
        '''

        result = self.query_api.query_data_frame(query)

        if not result.empty:
            # Pivot the data
            df = result.pivot_table(
                index='_time',
                columns='_field',
                values='_value',
                aggfunc='first'
            ).reset_index()

            df.columns.name = None
            return df

        return pd.DataFrame()

@register_widget({
    "name": "Real-time Trading Metrics",
    "description": "Real-time metrics from InfluxDB",
    "type": "chart",
    "endpoint": "influx_trading_metrics"
})
@app.get("/influx_trading_metrics")
def influx_trading_metrics(symbol: str = "AAPL", timeframe: str = "1h"):
    connector = InfluxDBConnector()
    df = connector.get_real_time_metrics(symbol, timeframe)

    if df.empty:
        return {"error": "No data available"}

    # Create real-time chart
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots

    fig = make_subplots(
        rows=3, cols=1,
        subplot_titles=('Price', 'Volume', 'Spread'),
        vertical_spacing=0.05
    )

    # Price
    fig.add_trace(
        go.Scatter(x=df['_time'], y=df['price'], name='Price'),
        row=1, col=1
    )

    # Volume
    fig.add_trace(
        go.Scatter(x=df['_time'], y=df['volume'], name='Volume', fill='tonexty'),
        row=2, col=1
    )

    # Spread
    fig.add_trace(
        go.Scatter(x=df['_time'], y=df['spread'], name='Spread'),
        row=3, col=1
    )

    fig.update_layout(
        title=f"Real-time Trading Metrics - {symbol}",
        height=800
    )

    return fig.to_json()
```

---

*Next: [Deployment & Production Guide](./06-deployment-production-guide.md)*
```