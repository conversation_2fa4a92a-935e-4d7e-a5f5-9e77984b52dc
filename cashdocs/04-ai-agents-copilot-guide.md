# AI Agents & Copilot Guide

## Table of Contents
- [OpenBB Copilot Overview](#openbb-copilot-overview)
- [Copilot Capabilities](#copilot-capabilities)
- [Custom AI Agent Development](#custom-ai-agent-development)
- [Agent Integration Patterns](#agent-integration-patterns)
- [Data Context Management](#data-context-management)
- [Advanced AI Features](#advanced-ai-features)
- [Best Practices](#best-practices)
- [Examples](#examples)

## OpenBB Copilot Overview

OpenBB Copilot is an AI-powered research assistant that integrates seamlessly with OpenBB Workspace. It can analyze data from widgets, process uploaded files, generate insights, and interact with users through natural language.

### Core Capabilities
- **Data Analysis**: Process widget data and uploaded files
- **Chart Generation**: Create visualizations from data
- **Research Assistance**: Answer financial questions and provide insights
- **Web Search**: Access real-time information
- **Context Awareness**: Understand dashboard context and user intent

### How Copilot Works
1. **Data Access**: Automatically accesses dashboard widgets and uploaded files
2. **Context Building**: Builds context from available data sources
3. **Query Processing**: Processes natural language queries
4. **Response Generation**: Generates insights, charts, and recommendations
5. **Citation**: Provides sources for all data and analysis

## Copilot Capabilities

### Data Source Integration

#### Widget Data Access
Copilot automatically has access to:
- All widgets on the current dashboard
- Widget parameters and configurations
- Real-time data updates
- Historical data context

#### File Processing
Supported file types:
- **PDF**: Extract text and analyze documents
- **CSV**: Process tabular data
- **XLSX**: Excel spreadsheet analysis
- **TXT**: Plain text document analysis

#### External Data Access
- OpenBB API endpoints
- Custom backend data sources
- Web search capabilities
- Real-time market data

### Interaction Patterns

#### Basic Queries
```
"What's the current price of AAPL?"
"Show me the P/E ratio for tech stocks"
"Create a chart of TSLA price over the last year"
"Analyze the earnings data from the uploaded PDF"
```

#### Advanced Analysis
```
"Compare the performance of AAPL, GOOGL, and MSFT over the last 5 years"
"What are the key trends in the semiconductor sector?"
"Analyze the correlation between oil prices and airline stocks"
"Generate a risk assessment for my portfolio"
```

#### Web-Enhanced Queries
```
"@web What's the latest news about Tesla earnings?"
"@web Search for recent Fed interest rate decisions"
"@web Find analyst recommendations for NVDA"
```

### Artifact Generation

#### Charts and Visualizations
Copilot can generate:
- Line charts for time series data
- Bar charts for comparisons
- Scatter plots for correlations
- Tables for structured data

#### Text Artifacts
- Market summaries
- Research reports
- Risk assessments
- Investment recommendations

#### Interactive Elements
- Clickable charts
- Filterable tables
- Dynamic dashboards
- Real-time updates

## Custom AI Agent Development

### Agent Architecture
```python
from fastapi import FastAPI
from pydantic import BaseModel
from typing import List, Dict, Any

app = FastAPI(title="Custom AI Agent")

class AgentRequest(BaseModel):
    query: str
    context: Dict[str, Any]
    user_id: str
    session_id: str

class AgentResponse(BaseModel):
    response: str
    artifacts: List[Dict[str, Any]]
    citations: List[Dict[str, str]]
    confidence: float

@app.post("/agent")
async def process_query(request: AgentRequest) -> AgentResponse:
    # Process the query with your custom AI logic
    response = await process_with_custom_llm(request)
    return response
```

### Basic Agent Implementation
```python
import openai
from typing import Dict, Any

class CustomFinancialAgent:
    def __init__(self, api_key: str, model: str = "gpt-4"):
        self.client = openai.OpenAI(api_key=api_key)
        self.model = model
    
    async def process_query(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        # Build context from widget data
        context_text = self.build_context(context)
        
        # Create system prompt
        system_prompt = f"""
        You are a financial analysis AI assistant. You have access to the following data:
        {context_text}
        
        Provide accurate, data-driven financial analysis and insights.
        Always cite your sources and be transparent about limitations.
        """
        
        # Generate response
        response = await self.client.chat.completions.create(
            model=self.model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": query}
            ]
        )
        
        return {
            "response": response.choices[0].message.content,
            "artifacts": [],
            "citations": self.extract_citations(context),
            "confidence": 0.85
        }
    
    def build_context(self, context: Dict[str, Any]) -> str:
        """Build context string from widget data"""
        context_parts = []
        
        # Process widget data
        if "widgets" in context:
            for widget_id, widget_data in context["widgets"].items():
                context_parts.append(f"Widget: {widget_id}")
                context_parts.append(f"Data: {widget_data}")
        
        # Process uploaded files
        if "files" in context:
            for file_info in context["files"]:
                context_parts.append(f"File: {file_info['name']}")
                context_parts.append(f"Content: {file_info['content']}")
        
        return "\n".join(context_parts)
    
    def extract_citations(self, context: Dict[str, Any]) -> List[Dict[str, str]]:
        """Extract citation information"""
        citations = []
        
        if "widgets" in context:
            for widget_id in context["widgets"]:
                citations.append({
                    "type": "widget",
                    "source": widget_id,
                    "description": f"Data from {widget_id} widget"
                })
        
        return citations
```

### Advanced Agent Features

#### Function Calling
```python
import json
from typing import List

class AdvancedAgent:
    def __init__(self):
        self.functions = [
            {
                "name": "get_stock_data",
                "description": "Get historical stock data",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "symbol": {"type": "string"},
                        "period": {"type": "string"}
                    }
                }
            },
            {
                "name": "calculate_technical_indicators",
                "description": "Calculate technical indicators",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "data": {"type": "array"},
                        "indicators": {"type": "array"}
                    }
                }
            }
        ]
    
    async def process_with_functions(self, query: str, context: Dict[str, Any]):
        response = await self.client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a financial AI with access to market data functions."},
                {"role": "user", "content": query}
            ],
            functions=self.functions,
            function_call="auto"
        )
        
        # Handle function calls
        if response.choices[0].message.function_call:
            function_name = response.choices[0].message.function_call.name
            function_args = json.loads(response.choices[0].message.function_call.arguments)
            
            # Execute function
            result = await self.execute_function(function_name, function_args)
            
            # Continue conversation with function result
            return await self.process_function_result(query, result)
        
        return response.choices[0].message.content
    
    async def execute_function(self, function_name: str, args: Dict[str, Any]):
        """Execute the requested function"""
        if function_name == "get_stock_data":
            return await self.get_stock_data(args["symbol"], args["period"])
        elif function_name == "calculate_technical_indicators":
            return await self.calculate_technical_indicators(args["data"], args["indicators"])
        
        return None
```

#### Multi-Modal Analysis
```python
import base64
from PIL import Image
import io

class MultiModalAgent:
    async def process_image(self, image_data: str, query: str) -> Dict[str, Any]:
        """Process image data (charts, documents, etc.)"""
        # Decode base64 image
        image_bytes = base64.b64decode(image_data)
        image = Image.open(io.BytesIO(image_bytes))
        
        # Use vision model for analysis
        response = await self.client.chat.completions.create(
            model="gpt-4-vision-preview",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": query},
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}
                        }
                    ]
                }
            ]
        )
        
        return {
            "response": response.choices[0].message.content,
            "image_analysis": True
        }
```

## Agent Integration Patterns

### Widget-Specific Agents
```python
@register_widget({
    "name": "AI Stock Analyst",
    "description": "AI-powered stock analysis",
    "type": "markdown",
    "endpoint": "ai_stock_analysis",
    "params": [
        {
            "type": "text",
            "paramName": "symbol",
            "value": "AAPL",
            "label": "Stock Symbol"
        },
        {
            "type": "text",
            "paramName": "analysis_type",
            "value": "fundamental",
            "label": "Analysis Type",
            "options": [
                {"label": "Fundamental", "value": "fundamental"},
                {"label": "Technical", "value": "technical"},
                {"label": "Sentiment", "value": "sentiment"}
            ]
        }
    ]
})
@app.get("/ai_stock_analysis")
async def ai_stock_analysis(symbol: str = "AAPL", analysis_type: str = "fundamental"):
    # Get stock data
    stock_data = obb.equity.price.historical(symbol)
    fundamentals = obb.equity.fundamental.overview(symbol)
    
    # Build context
    context = {
        "symbol": symbol,
        "price_data": stock_data.to_dict(),
        "fundamentals": fundamentals.to_dict(),
        "analysis_type": analysis_type
    }
    
    # Query AI agent
    agent = CustomFinancialAgent(api_key="your_key")
    query = f"Provide a {analysis_type} analysis of {symbol} based on the available data."
    
    result = await agent.process_query(query, context)
    return result["response"]
```

### Real-time Agent Updates
```python
from fastapi import WebSocket
import asyncio

class RealTimeAgent:
    def __init__(self):
        self.active_connections = []
    
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
    
    async def start_monitoring(self, symbols: List[str]):
        """Monitor stocks and provide real-time insights"""
        while True:
            for symbol in symbols:
                # Get latest data
                data = await self.get_latest_data(symbol)
                
                # Analyze for significant changes
                insight = await self.analyze_changes(symbol, data)
                
                if insight:
                    # Broadcast to connected clients
                    await self.broadcast_insight(insight)
            
            await asyncio.sleep(60)  # Check every minute
    
    async def analyze_changes(self, symbol: str, data: Dict[str, Any]) -> str:
        """Analyze data for significant changes"""
        # Implement your analysis logic
        price_change = data.get("price_change_percent", 0)
        
        if abs(price_change) > 5:  # Significant move
            agent = CustomFinancialAgent(api_key="your_key")
            query = f"Analyze the {price_change:.2f}% move in {symbol} today. What might be causing this?"
            
            result = await agent.process_query(query, {"symbol": symbol, "data": data})
            return result["response"]
        
        return None

@app.websocket("/ws_ai_insights")
async def websocket_ai_insights(websocket: WebSocket):
    agent = RealTimeAgent()
    await agent.connect(websocket)
    
    # Start monitoring
    symbols = ["AAPL", "GOOGL", "MSFT", "TSLA"]
    await agent.start_monitoring(symbols)
```

## Data Context Management

### Context Building Strategies
```python
class ContextManager:
    def __init__(self):
        self.context_cache = {}
    
    def build_widget_context(self, widget_data: Dict[str, Any]) -> str:
        """Build context from widget data"""
        context_parts = []
        
        for widget_id, data in widget_data.items():
            widget_info = data.get("config", {})
            widget_results = data.get("results", [])
            
            context_parts.append(f"## {widget_info.get('name', widget_id)}")
            context_parts.append(f"Description: {widget_info.get('description', 'No description')}")
            
            # Summarize data
            if isinstance(widget_results, list) and widget_results:
                context_parts.append(f"Data points: {len(widget_results)}")
                
                # Add sample data
                if len(widget_results) > 0:
                    sample = widget_results[0]
                    context_parts.append(f"Sample data: {sample}")
        
        return "\n".join(context_parts)
    
    def build_file_context(self, files: List[Dict[str, Any]]) -> str:
        """Build context from uploaded files"""
        context_parts = []
        
        for file_info in files:
            context_parts.append(f"## File: {file_info['name']}")
            context_parts.append(f"Type: {file_info['type']}")
            context_parts.append(f"Size: {file_info['size']} bytes")
            
            # Add content summary
            content = file_info.get("content", "")
            if content:
                # Truncate long content
                summary = content[:500] + "..." if len(content) > 500 else content
                context_parts.append(f"Content preview: {summary}")
        
        return "\n".join(context_parts)
    
    def get_relevant_context(self, query: str, available_context: Dict[str, Any]) -> str:
        """Extract relevant context based on query"""
        # Use keyword matching or embedding similarity
        relevant_parts = []
        
        query_lower = query.lower()
        
        # Check for specific symbols or companies
        if any(symbol in query_lower for symbol in ["aapl", "apple", "googl", "google"]):
            # Include relevant stock data
            pass
        
        # Check for analysis types
        if any(term in query_lower for term in ["technical", "fundamental", "sentiment"]):
            # Include relevant analysis data
            pass
        
        return "\n".join(relevant_parts)
```

### Memory Management
```python
class AgentMemory:
    def __init__(self):
        self.conversation_history = {}
        self.user_preferences = {}
        self.analysis_cache = {}
    
    def store_conversation(self, session_id: str, query: str, response: str):
        """Store conversation for context"""
        if session_id not in self.conversation_history:
            self.conversation_history[session_id] = []
        
        self.conversation_history[session_id].append({
            "timestamp": datetime.now(),
            "query": query,
            "response": response
        })
        
        # Keep only last 10 exchanges
        if len(self.conversation_history[session_id]) > 10:
            self.conversation_history[session_id] = self.conversation_history[session_id][-10:]
    
    def get_conversation_context(self, session_id: str) -> str:
        """Get conversation context for continuity"""
        history = self.conversation_history.get(session_id, [])
        
        context_parts = []
        for exchange in history[-3:]:  # Last 3 exchanges
            context_parts.append(f"Previous Q: {exchange['query']}")
            context_parts.append(f"Previous A: {exchange['response'][:200]}...")
        
        return "\n".join(context_parts)
```

## Advanced AI Features

### Sentiment Analysis Integration
```python
from textblob import TextBlob
import yfinance as yf

class SentimentAgent:
    async def analyze_stock_sentiment(self, symbol: str) -> Dict[str, Any]:
        """Analyze sentiment around a stock"""
        # Get news data
        ticker = yf.Ticker(symbol)
        news = ticker.news
        
        sentiments = []
        for article in news[:10]:  # Analyze last 10 articles
            text = article.get("summary", "")
            blob = TextBlob(text)
            sentiments.append({
                "title": article.get("title", ""),
                "sentiment": blob.sentiment.polarity,
                "subjectivity": blob.sentiment.subjectivity
            })
        
        # Calculate overall sentiment
        avg_sentiment = sum(s["sentiment"] for s in sentiments) / len(sentiments)
        
        return {
            "symbol": symbol,
            "overall_sentiment": avg_sentiment,
            "sentiment_label": self.get_sentiment_label(avg_sentiment),
            "articles_analyzed": len(sentiments),
            "detailed_sentiments": sentiments
        }
    
    def get_sentiment_label(self, score: float) -> str:
        if score > 0.1:
            return "Positive"
        elif score < -0.1:
            return "Negative"
        else:
            return "Neutral"
```

### Risk Assessment Agent
```python
import numpy as np
import pandas as pd

class RiskAssessmentAgent:
    async def assess_portfolio_risk(self, portfolio: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Assess portfolio risk metrics"""
        risk_metrics = {}
        
        for holding in portfolio:
            symbol = holding["symbol"]
            weight = holding["weight"]
            
            # Get historical data
            data = obb.equity.price.historical(symbol, start_date="-2y")
            df = data.to_dataframe()
            
            # Calculate risk metrics
            returns = df["close"].pct_change().dropna()
            
            risk_metrics[symbol] = {
                "volatility": returns.std() * np.sqrt(252),  # Annualized
                "var_95": np.percentile(returns, 5),
                "max_drawdown": self.calculate_max_drawdown(df["close"]),
                "sharpe_ratio": self.calculate_sharpe_ratio(returns),
                "weight": weight
            }
        
        # Calculate portfolio-level metrics
        portfolio_risk = self.calculate_portfolio_risk(risk_metrics)
        
        return {
            "individual_risks": risk_metrics,
            "portfolio_risk": portfolio_risk,
            "recommendations": self.generate_risk_recommendations(portfolio_risk)
        }
    
    def calculate_max_drawdown(self, prices: pd.Series) -> float:
        """Calculate maximum drawdown"""
        peak = prices.expanding().max()
        drawdown = (prices - peak) / peak
        return drawdown.min()
    
    def calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio"""
        excess_returns = returns.mean() * 252 - risk_free_rate
        volatility = returns.std() * np.sqrt(252)
        return excess_returns / volatility if volatility > 0 else 0
```

## Best Practices

### 1. Agent Design Principles
- **Clear Purpose**: Define specific use cases for each agent
- **Context Awareness**: Leverage available data effectively
- **User Experience**: Provide clear, actionable insights
- **Performance**: Optimize response times and accuracy

### 2. Data Handling
- **Validation**: Validate all input data
- **Sanitization**: Clean and normalize data before processing
- **Caching**: Cache expensive computations
- **Error Handling**: Gracefully handle data issues

### 3. Response Quality
- **Accuracy**: Ensure factual correctness
- **Relevance**: Stay focused on user queries
- **Citations**: Always provide data sources
- **Clarity**: Use clear, professional language

### 4. Security Considerations
- **API Keys**: Secure storage of credentials
- **Data Privacy**: Protect sensitive information
- **Rate Limiting**: Implement appropriate limits
- **Authentication**: Verify user permissions

## Examples

### Complete AI-Powered Research Assistant
```python
class ResearchAssistant:
    def __init__(self, openai_key: str):
        self.client = openai.OpenAI(api_key=openai_key)
        self.memory = AgentMemory()
        self.context_manager = ContextManager()
    
    async def research_stock(self, symbol: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive stock research"""
        # Gather data
        price_data = obb.equity.price.historical(symbol, start_date="-1y")
        fundamentals = obb.equity.fundamental.overview(symbol)
        news_sentiment = await self.analyze_sentiment(symbol)
        
        # Build comprehensive context
        research_context = {
            "symbol": symbol,
            "price_data": price_data.to_dict(),
            "fundamentals": fundamentals.to_dict(),
            "sentiment": news_sentiment,
            "market_context": await self.get_market_context()
        }
        
        # Generate research report
        query = f"""
        Provide a comprehensive research analysis for {symbol} including:
        1. Current valuation assessment
        2. Technical analysis summary
        3. Fundamental strengths and weaknesses
        4. Market sentiment analysis
        5. Investment recommendation with rationale
        """
        
        response = await self.client.chat.completions.create(
            model="gpt-4",
            messages=[
                {
                    "role": "system",
                    "content": "You are a professional equity research analyst. Provide thorough, data-driven analysis."
                },
                {
                    "role": "user",
                    "content": f"{query}\n\nData: {json.dumps(research_context, indent=2)}"
                }
            ]
        )
        
        return {
            "symbol": symbol,
            "research_report": response.choices[0].message.content,
            "data_sources": ["OpenBB", "News Sentiment", "Market Data"],
            "confidence": 0.9,
            "timestamp": datetime.now().isoformat()
        }

# Integration with OpenBB Workspace
@register_widget({
    "name": "AI Research Assistant",
    "description": "AI-powered comprehensive stock research",
    "type": "markdown",
    "endpoint": "ai_research",
    "category": "AI Analysis"
})
@app.get("/ai_research")
async def ai_research(symbol: str = "AAPL"):
    assistant = ResearchAssistant(openai_key="your_key")
    result = await assistant.research_stock(symbol, {})
    return result["research_report"]
```

---

*Next: [Database Connectors Reference](./05-database-connectors-reference.md)*
