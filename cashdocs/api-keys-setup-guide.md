# Complete API Keys Setup Guide

## 🔑 Essential API Keys for Full OpenBB Functionality

### **Free Tier APIs (Start Here)**

#### 1. Alpha Vantage (Free: 25 calls/day)
```bash
# Get key: https://www.alphavantage.co/support/#api-key
OPENBB_ALPHA_VANTAGE_API_KEY=your_key_here
```
**Provides**: Stock prices, forex, crypto, technical indicators

#### 2. FRED (Federal Reserve Economic Data) - FREE
```bash
# Get key: https://fred.stlouisfed.org/docs/api/api_key.html
OPENBB_FRED_API_KEY=your_key_here
```
**Provides**: Economic indicators, GDP, inflation, unemployment

#### 3. Yahoo Finance (Free via yfinance)
```bash
# No API key needed - built into OpenBB
# Provides: Stock prices, options, fundamentals
```

#### 4. Polygon.io (Free: 5 calls/minute)
```bash
# Get key: https://polygon.io/
OPENBB_POLYGON_API_KEY=your_key_here
```
**Provides**: Real-time and historical market data

### **Premium Financial Data APIs**

#### 5. Financial Modeling Prep ($15-299/month)
```bash
# Get key: https://financialmodelingprep.com/developer/docs
OPENBB_FMP_API_KEY=your_key_here
```
**Provides**: Comprehensive fundamentals, estimates, insider trading

#### 6. Benzinga ($99-999/month)
```bash
# Get key: https://www.benzinga.com/apis/
OPENBB_BENZINGA_API_KEY=your_key_here
```
**Provides**: News, earnings calendar, analyst ratings

#### 7. Intrinio ($99-2000/month)
```bash
# Get key: https://intrinio.com/
OPENBB_INTRINIO_API_KEY=your_key_here
```
**Provides**: Real-time data, fundamentals, options

#### 8. Tiingo ($10-500/month)
```bash
# Get key: https://api.tiingo.com/
OPENBB_TIINGO_API_KEY=your_key_here
```
**Provides**: End-of-day and intraday data, news

### **Cryptocurrency APIs**

#### 9. CoinMarketCap (Free: 333 calls/day)
```bash
# Get key: https://coinmarketcap.com/api/
OPENBB_CMC_API_KEY=your_key_here
```

#### 10. CoinGecko (Free: 50 calls/minute)
```bash
# Get key: https://www.coingecko.com/en/api
OPENBB_COINGECKO_API_KEY=your_key_here
```

#### 11. Binance (Free)
```bash
# Get key: https://www.binance.com/en/binance-api
OPENBB_BINANCE_API_KEY=your_key_here
OPENBB_BINANCE_SECRET_KEY=your_secret_here
```

#### 12. Coinbase (Free)
```bash
# Get key: https://developers.coinbase.com/
OPENBB_COINBASE_API_KEY=your_key_here
OPENBB_COINBASE_SECRET_KEY=your_secret_here
```

### **News & Sentiment APIs**

#### 13. News API (Free: 1000 requests/day)
```bash
# Get key: https://newsapi.org/
OPENBB_NEWS_API_KEY=your_key_here
```

#### 14. Reddit API (Free)
```bash
# Get credentials: https://www.reddit.com/prefs/apps
OPENBB_REDDIT_CLIENT_ID=your_client_id
OPENBB_REDDIT_CLIENT_SECRET=your_client_secret
OPENBB_REDDIT_USERNAME=your_username
OPENBB_REDDIT_PASSWORD=your_password
```

#### 15. Twitter API (Free tier available)
```bash
# Get key: https://developer.twitter.com/
OPENBB_TWITTER_BEARER_TOKEN=your_bearer_token
OPENBB_TWITTER_API_KEY=your_api_key
OPENBB_TWITTER_API_SECRET=your_api_secret
```

### **AI & Analysis APIs**

#### 16. OpenAI ($0.002-0.12 per 1K tokens)
```bash
# Get key: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_key_here
```

#### 17. Anthropic Claude ($0.25-15 per 1M tokens)
```bash
# Get key: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_key_here
```

#### 18. Google AI (Free tier available)
```bash
# Get key: https://ai.google.dev/
GOOGLE_AI_API_KEY=your_key_here
```

### **Custom APIs (Your Examples)**

#### 19. Taptools (Cardano)
```bash
# Get key: https://taptools.io/
TAPTOOLS_API_KEY=your_key_here
```

#### 20. Blockfrost (Cardano)
```bash
# Get key: https://blockfrost.io/
BLOCKFROST_PROJECT_ID=your_project_id
```

#### 21. MASTRA Agent (Your Custom Agent)
```bash
# Your custom MASTRA agent endpoint
MASTRA_AGENT_URL=http://your-mastra-agent.com
MASTRA_AGENT_API_KEY=your_mastra_key
```

## 🚀 Complete Environment Setup

### .env File Template
```bash
# ============================================
# OPENBB CORE API KEYS
# ============================================

# Free Tier APIs
OPENBB_ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
OPENBB_FRED_API_KEY=your_fred_key
OPENBB_POLYGON_API_KEY=your_polygon_key

# Premium Financial Data
OPENBB_FMP_API_KEY=your_fmp_key
OPENBB_BENZINGA_API_KEY=your_benzinga_key
OPENBB_INTRINIO_API_KEY=your_intrinio_key
OPENBB_TIINGO_API_KEY=your_tiingo_key

# Cryptocurrency
OPENBB_CMC_API_KEY=your_cmc_key
OPENBB_COINGECKO_API_KEY=your_coingecko_key
OPENBB_BINANCE_API_KEY=your_binance_key
OPENBB_BINANCE_SECRET_KEY=your_binance_secret

# News & Sentiment
OPENBB_NEWS_API_KEY=your_news_api_key
OPENBB_REDDIT_CLIENT_ID=your_reddit_client_id
OPENBB_REDDIT_CLIENT_SECRET=your_reddit_secret
OPENBB_TWITTER_BEARER_TOKEN=your_twitter_token

# AI Services
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_AI_API_KEY=your_google_ai_key

# ============================================
# CUSTOM API INTEGRATIONS
# ============================================

# Cardano Ecosystem
TAPTOOLS_API_KEY=your_taptools_key
BLOCKFROST_PROJECT_ID=your_blockfrost_project_id

# Your Custom Agents
MASTRA_AGENT_URL=http://your-mastra-agent.com
MASTRA_AGENT_API_KEY=your_mastra_key

# Additional Custom APIs
CUSTOM_API_1_KEY=your_custom_key_1
CUSTOM_API_2_URL=https://api.example.com
CUSTOM_API_3_TOKEN=your_custom_token

# ============================================
# DATABASE & INFRASTRUCTURE
# ============================================

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/openbb_db
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your_super_secret_key_here_make_it_long_and_random
JWT_SECRET_KEY=another_secret_key_for_jwt_tokens

# Application Settings
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# CORS Settings
CORS_ORIGINS=["https://pro.openbb.co", "http://localhost:3000"]
```

## 📋 API Key Priority Guide

### **Start with these (Free & Essential)**
1. ✅ **Alpha Vantage** - Free stock data
2. ✅ **FRED** - Free economic data  
3. ✅ **Yahoo Finance** - Built-in, no key needed
4. ✅ **OpenAI** - AI analysis ($5-20/month typical usage)

### **Add these for Professional Use**
5. 🔥 **Financial Modeling Prep** - Comprehensive fundamentals
6. 🔥 **Polygon.io** - Real-time market data
7. 🔥 **Benzinga** - News and earnings
8. 🔥 **Your Custom APIs** - Taptools, MASTRA, etc.

### **Enterprise Level**
9. 💎 **Intrinio** - Institutional-grade data
10. 💎 **Bloomberg API** - Premium financial data
11. 💎 **Refinitiv** - Professional market data

## 🛠️ Setup Instructions

### 1. Create API Keys Configuration
```python
# config/api_keys.py
import os
from typing import Dict, Optional

class APIKeysManager:
    def __init__(self):
        self.keys = self._load_keys()
    
    def _load_keys(self) -> Dict[str, Optional[str]]:
        return {
            # Financial Data
            "alpha_vantage": os.getenv("OPENBB_ALPHA_VANTAGE_API_KEY"),
            "fred": os.getenv("OPENBB_FRED_API_KEY"),
            "polygon": os.getenv("OPENBB_POLYGON_API_KEY"),
            "fmp": os.getenv("OPENBB_FMP_API_KEY"),
            
            # Crypto
            "cmc": os.getenv("OPENBB_CMC_API_KEY"),
            "binance_key": os.getenv("OPENBB_BINANCE_API_KEY"),
            "binance_secret": os.getenv("OPENBB_BINANCE_SECRET_KEY"),
            
            # AI
            "openai": os.getenv("OPENAI_API_KEY"),
            "anthropic": os.getenv("ANTHROPIC_API_KEY"),
            
            # Custom APIs
            "taptools": os.getenv("TAPTOOLS_API_KEY"),
            "blockfrost": os.getenv("BLOCKFROST_PROJECT_ID"),
            "mastra_url": os.getenv("MASTRA_AGENT_URL"),
            "mastra_key": os.getenv("MASTRA_AGENT_API_KEY"),
        }
    
    def get_key(self, service: str) -> Optional[str]:
        return self.keys.get(service)
    
    def is_configured(self, service: str) -> bool:
        return self.get_key(service) is not None
    
    def get_configured_services(self) -> list:
        return [service for service, key in self.keys.items() if key]
    
    def validate_keys(self) -> Dict[str, bool]:
        """Validate that API keys are working"""
        validation_results = {}
        
        # Test each API key
        for service, key in self.keys.items():
            if key:
                try:
                    # Add validation logic for each service
                    validation_results[service] = self._test_api_key(service, key)
                except Exception as e:
                    validation_results[service] = False
            else:
                validation_results[service] = False
        
        return validation_results
    
    def _test_api_key(self, service: str, key: str) -> bool:
        """Test if API key is valid"""
        # Implement validation for each service
        # This is a simplified example
        return len(key) > 10  # Basic validation

# Usage
api_keys = APIKeysManager()
```

### 2. Dynamic Provider Selection
```python
# services/data_service.py
class DataService:
    def __init__(self):
        self.api_keys = APIKeysManager()
        self.providers = self._get_available_providers()
    
    def _get_available_providers(self) -> Dict[str, bool]:
        return {
            "alpha_vantage": self.api_keys.is_configured("alpha_vantage"),
            "polygon": self.api_keys.is_configured("polygon"),
            "fmp": self.api_keys.is_configured("fmp"),
            "yfinance": True,  # Always available
        }
    
    def get_stock_data(self, symbol: str, provider: str = None):
        """Get stock data with automatic provider fallback"""
        if provider and self.providers.get(provider):
            return self._fetch_from_provider(symbol, provider)
        
        # Fallback chain
        for fallback_provider in ["fmp", "alpha_vantage", "polygon", "yfinance"]:
            if self.providers.get(fallback_provider):
                try:
                    return self._fetch_from_provider(symbol, fallback_provider)
                except Exception as e:
                    print(f"Provider {fallback_provider} failed: {e}")
                    continue
        
        raise Exception("No working data providers available")
```

### 3. API Status Dashboard Widget
```python
@register_widget({
    "name": "API Status Dashboard",
    "description": "Monitor API key status and usage",
    "type": "table",
    "endpoint": "api_status",
    "category": "System"
})
@app.get("/api_status")
def api_status():
    api_keys = APIKeysManager()
    validation_results = api_keys.validate_keys()
    
    status_data = []
    for service, is_valid in validation_results.items():
        status_data.append({
            "service": service.replace("_", " ").title(),
            "status": "✅ Active" if is_valid else "❌ Inactive",
            "configured": "Yes" if api_keys.is_configured(service) else "No",
            "type": "Free" if service in ["fred", "yfinance"] else "Paid"
        })
    
    return status_data
```

## 💡 Pro Tips

### **Cost Optimization**
1. Start with free APIs
2. Use caching to reduce API calls
3. Implement rate limiting
4. Monitor usage with dashboards

### **Reliability**
1. Implement provider fallbacks
2. Add retry logic with exponential backoff
3. Monitor API health
4. Set up alerts for failures

### **Security**
1. Never commit API keys to git
2. Use environment variables
3. Rotate keys regularly
4. Implement proper access controls

The beauty of this setup is that you can start with just a few free APIs and gradually add premium services as your needs grow. The system automatically adapts to whatever APIs you have configured!
