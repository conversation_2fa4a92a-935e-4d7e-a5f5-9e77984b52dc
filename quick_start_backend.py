#!/usr/bin/env python3
"""
OpenBB Quick Start Backend
A simple backend to get started with OpenBB Workspace integration
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import json
from typing import Optional, Dict, Any
import pandas as pd
from datetime import datetime, timedelta
import os

# Try to import OpenBB
try:
    from openbb import obb
    OPENBB_AVAILABLE = True
    print("✅ OpenBB imported successfully!")
except ImportError as e:
    OPENBB_AVAILABLE = False
    print(f"❌ OpenBB not available: {e}")
    print("Please install OpenBB: pip install openbb")

# Widget registry
WIDGETS = {}

def register_widget(config):
    """Decorator to register widgets"""
    def decorator(func):
        endpoint = config.get("endpoint")
        if endpoint:
            config["id"] = endpoint
            WIDGETS[endpoint] = config
        return func
    return decorator

# FastAPI app
app = FastAPI(
    title="OpenBB Quick Start Backend",
    description="Quick start backend for OpenBB Workspace integration",
    version="1.0.0"
)

# CORS middleware - allows OpenBB Workspace to connect
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://pro.openbb.co", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {
        "message": "OpenBB Quick Start Backend",
        "status": "running",
        "openbb_available": OPENBB_AVAILABLE,
        "endpoints": list(WIDGETS.keys())
    }

@app.get("/widgets.json")
def get_widgets():
    """Return widget configurations for OpenBB Workspace"""
    return WIDGETS

@app.get("/health")
def health_check():
    return {
        "status": "healthy",
        "openbb_available": OPENBB_AVAILABLE,
        "timestamp": datetime.now().isoformat()
    }

# ============================================
# BASIC WIDGETS (No API Keys Required)
# ============================================

@register_widget({
    "name": "Stock Price Quote",
    "description": "Current stock price and basic info",
    "type": "metric",
    "endpoint": "stock_quote",
    "category": "Equity",
    "params": [
        {
            "type": "text",
            "paramName": "symbol",
            "value": "AAPL",
            "label": "Stock Symbol"
        }
    ]
})
@app.get("/stock_quote")
def stock_quote(symbol: str = "AAPL"):
    """Get current stock quote - uses yfinance (no API key needed)"""
    if not OPENBB_AVAILABLE:
        raise HTTPException(status_code=503, detail="OpenBB not available")
    
    try:
        # Get quote data
        quote = obb.equity.price.quote(symbol)
        if not quote.results:
            raise HTTPException(status_code=404, detail=f"No data found for {symbol}")
        
        result = quote.results[0]
        
        return {
            "value": f"${result.last_price:.2f}",
            "title": f"{symbol} Current Price",
            "subtitle": f"Volume: {result.volume:,}",
            "change": f"${result.change:.2f}",
            "change_percent": f"{result.change_percent:.2f}%",
            "currency": "USD"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@register_widget({
    "name": "Stock Price History",
    "description": "Historical stock price data",
    "type": "table",
    "endpoint": "stock_history",
    "category": "Equity",
    "data": {
        "table": {
            "enableCharts": True,
            "chartView": {
                "enabled": True,
                "chartType": "line",
                "cellRangeCols": {
                    "line": ["date", "close", "volume"]
                }
            },
            "columnsDefs": [
                {
                    "field": "date",
                    "headerName": "Date",
                    "chartDataType": "category",
                    "cellDataType": "date"
                },
                {
                    "field": "close",
                    "headerName": "Close Price",
                    "chartDataType": "series",
                    "cellDataType": "number",
                    "prefix": "$"
                },
                {
                    "field": "volume",
                    "headerName": "Volume",
                    "chartDataType": "series",
                    "cellDataType": "number",
                    "formatterFn": "int"
                }
            ]
        }
    },
    "params": [
        {
            "type": "text",
            "paramName": "symbol",
            "value": "AAPL",
            "label": "Stock Symbol"
        },
        {
            "type": "date",
            "paramName": "start_date",
            "value": "$currentDate-1y",
            "label": "Start Date"
        }
    ]
})
@app.get("/stock_history")
def stock_history(symbol: str = "AAPL", start_date: str = None):
    """Get historical stock data"""
    if not OPENBB_AVAILABLE:
        raise HTTPException(status_code=503, detail="OpenBB not available")
    
    try:
        # Default to 1 year ago if no start date
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
        
        # Get historical data
        data = obb.equity.price.historical(symbol, start_date=start_date)
        df = data.to_dataframe()
        
        # Format for frontend
        df['date'] = df.index.strftime('%Y-%m-%d')
        df = df.reset_index(drop=True)
        
        return df[['date', 'open', 'high', 'low', 'close', 'volume']].to_dict('records')
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

# ============================================
# API KEY WIDGETS (Require API Keys)
# ============================================

@register_widget({
    "name": "Economic Indicators",
    "description": "US Economic indicators from FRED",
    "type": "table",
    "endpoint": "economic_indicators",
    "category": "Economy",
    "params": [
        {
            "type": "text",
            "paramName": "indicator",
            "value": "GDP",
            "label": "Indicator",
            "options": [
                {"label": "GDP", "value": "GDP"},
                {"label": "Unemployment", "value": "UNRATE"},
                {"label": "Inflation (CPI)", "value": "CPIAUCSL"},
                {"label": "Federal Funds Rate", "value": "FEDFUNDS"}
            ]
        }
    ]
})
@app.get("/economic_indicators")
def economic_indicators(indicator: str = "GDP"):
    """Get economic indicators - requires FRED API key"""
    if not OPENBB_AVAILABLE:
        raise HTTPException(status_code=503, detail="OpenBB not available")
    
    try:
        # This will use FRED API if key is configured
        if indicator == "GDP":
            data = obb.economy.gdp.real("US")
        elif indicator == "UNRATE":
            data = obb.economy.fred_series("UNRATE")
        elif indicator == "CPIAUCSL":
            data = obb.economy.fred_series("CPIAUCSL")
        elif indicator == "FEDFUNDS":
            data = obb.economy.fred_series("FEDFUNDS")
        else:
            data = obb.economy.fred_series(indicator)
        
        df = data.to_dataframe()
        df = df.reset_index()
        
        # Format date column
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date']).dt.strftime('%Y-%m-%d')
        
        return df.tail(50).to_dict('records')  # Last 50 data points
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error fetching {indicator}: {str(e)}")

@register_widget({
    "name": "API Status Check",
    "description": "Check which API keys are configured",
    "type": "table",
    "endpoint": "api_status",
    "category": "System"
})
@app.get("/api_status")
def api_status():
    """Check API key status"""
    if not OPENBB_AVAILABLE:
        return [{"service": "OpenBB", "status": "❌ Not Available", "note": "Please install OpenBB"}]
    
    status_data = []
    
    # Check various API keys
    api_checks = {
        "FRED": "fred_api_key",
        "Alpha Vantage": "alpha_vantage_api_key", 
        "Polygon": "polygon_api_key",
        "Yahoo Finance": None  # No key needed
    }
    
    for service, key_name in api_checks.items():
        if key_name is None:
            status = "✅ Available (No Key Required)"
        else:
            try:
                key_value = getattr(obb.user.credentials, key_name, None)
                status = "✅ Configured" if key_value else "❌ Not Configured"
            except:
                status = "❌ Error Checking"
        
        status_data.append({
            "service": service,
            "status": status,
            "key_required": "No" if key_name is None else "Yes"
        })
    
    return status_data

if __name__ == "__main__":
    import uvicorn
    print("\n🚀 Starting OpenBB Quick Start Backend...")
    print("📊 Available widgets:", len(WIDGETS))
    print("🔗 API Documentation: http://localhost:8000/docs")
    print("📋 Widgets Config: http://localhost:8000/widgets.json")
    print("❤️  Health Check: http://localhost:8000/health")
    print("\n💡 To connect to OpenBB Workspace:")
    print("   1. Go to https://pro.openbb.co/")
    print("   2. Add custom backend: http://localhost:8000")
    print("   3. Start exploring your widgets!")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
